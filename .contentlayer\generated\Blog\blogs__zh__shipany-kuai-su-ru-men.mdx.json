{"title": "ShipAny 快速入门：几小时内构建你的 AI SaaS", "slug": "getting-started-with-shipany", "description": "学习如何使用 ShipAny 强大的模板和组件快速构建和部署你的 AI SaaS 应用程序。", "coverImage": "https://images.unsplash.com/photo-1551288049-bebda4e38f71?w=800&h=400&fit=crop", "author": "ShipAny 团队", "authorImage": "https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=100&h=100&fit=crop&crop=face", "publishedAt": "2025-01-17T00:00:00.000Z", "featured": true, "tags": ["教程", "快速入门", "ai-saas"], "body": {"raw": "\n# ShipAny 快速入门\n\n欢迎使用 ShipAny！这份综合指南将带你使用我们强大的模板系统构建你的第一个 AI SaaS 应用程序。\n\n## 什么是 ShipAny？\n\nShipAny 是一个专为快速高效构建 AI SaaS 创业项目而设计的 Next.js 样板。通过预构建的组件、身份验证、支付处理和 AI 集成，你可以专注于你的独特价值主张，而不是样板代码。\n\n## 核心特性\n\n- **🚀 快速开发**：预构建的组件和模板\n- **🤖 AI 集成**：开箱即用的 AI SDK 集成\n- **💳 支付处理**：内置 Stripe 集成\n- **🌍 国际化**：多语言支持\n- **📱 响应式设计**：移动优先的方法\n- **🔐 身份验证**：安全的用户管理\n\n## 快速开始\n\n### 1. 克隆仓库\n\n```bash\ngit clone https://github.com/shipany/shipany-template\ncd shipany-template\n```\n\n### 2. 安装依赖\n\n```bash\npnpm install\n```\n\n### 3. 设置环境变量\n\n创建一个 `.env.local` 文件并配置：\n\n```env\nNEXT_PUBLIC_WEB_URL=http://localhost:3000\nDATABASE_URL=your_database_url\nNEXTAUTH_SECRET=your_secret\nSTRIPE_SECRET_KEY=your_stripe_key\n```\n\n### 4. 运行开发服务器\n\n```bash\npnpm dev\n```\n\n## 构建你的第一个功能\n\n让我们创建一个简单的 AI 驱动的文本生成器：\n\n### 1. 创建 API 路由\n\n```typescript\n// app/api/generate/route.ts\nimport { openai } from '@ai-sdk/openai'\nimport { generateText } from 'ai'\n\nexport async function POST(request: Request) {\n  const { prompt } = await request.json()\n  \n  const { text } = await generateText({\n    model: openai('gpt-3.5-turbo'),\n    prompt: `基于以下内容生成创意文本：${prompt}`,\n  })\n  \n  return Response.json({ text })\n}\n```\n\n### 2. 创建前端组件\n\n```tsx\n'use client'\n\nimport { useState } from 'react'\nimport { Button } from '@/components/ui/button'\nimport { Textarea } from '@/components/ui/textarea'\n\nexport function TextGenerator() {\n  const [prompt, setPrompt] = useState('')\n  const [result, setResult] = useState('')\n  const [loading, setLoading] = useState(false)\n\n  const handleGenerate = async () => {\n    setLoading(true)\n    try {\n      const response = await fetch('/api/generate', {\n        method: 'POST',\n        headers: { 'Content-Type': 'application/json' },\n        body: JSON.stringify({ prompt }),\n      })\n      const data = await response.json()\n      setResult(data.text)\n    } catch (error) {\n      console.error('错误:', error)\n    } finally {\n      setLoading(false)\n    }\n  }\n\n  return (\n    <div className=\"space-y-4\">\n      <Textarea\n        placeholder=\"输入你的提示...\"\n        value={prompt}\n        onChange={(e) => setPrompt(e.target.value)}\n      />\n      <Button onClick={handleGenerate} disabled={loading}>\n        {loading ? '生成中...' : '生成'}\n      </Button>\n      {result && (\n        <div className=\"p-4 bg-muted rounded-lg\">\n          {result}\n        </div>\n      )}\n    </div>\n  )\n}\n```\n\n## 下一步\n\n现在你已经设置好了基础，你可以：\n\n1. **自定义 UI**：修改组件以匹配你的品牌\n2. **添加更多 AI 功能**：集成额外的 AI 模型\n3. **设置支付**：为订阅配置 Stripe\n4. **部署**：部署到 Vercel 或你首选的平台\n\n## 结论\n\nShipAny 提供了快速构建和启动 AI SaaS 所需的一切。凭借其全面的功能集和开发者友好的架构，你可以专注于最重要的事情：为用户构建优秀的产品。\n\n准备好发布你的下一个 AI SaaS 了吗？今天就开始使用 ShipAny 吧！\n", "code": "var Component=(()=>{var Ve=Object.create;var C=Object.defineProperty;var ze=Object.getOwnPropertyDescriptor;var Ae=Object.getOwnPropertyNames;var Te=Object.getPrototypeOf,De=Object.prototype.hasOwnProperty;var M=(d,n)=>()=>(n||d((n={exports:{}}).exports,n),n.exports),je=(d,n)=>{for(var c in n)C(d,c,{get:n[c],enumerable:!0})},se=(d,n,c,y)=>{if(n&&typeof n==\"object\"||typeof n==\"function\")for(let h of Ae(n))!De.call(d,h)&&h!==c&&C(d,h,{get:()=>n[h],enumerable:!(y=ze(n,h))||y.enumerable});return d};var Oe=(d,n,c)=>(c=d!=null?Ve(Te(d)):{},se(n||!d||!d.__esModule?C(c,\"default\",{value:d,enumerable:!0}):c,d)),Re=d=>se(C({},\"__esModule\",{value:!0}),d);var ce=M(($e,ue)=>{ue.exports=React});var fe=M(U=>{\"use strict\";(function(){function d(e){if(e==null)return null;if(typeof e==\"function\")return e.$$typeof===ve?null:e.displayName||e.name||null;if(typeof e==\"string\")return e;switch(e){case z:return\"Fragment\";case Ee:return\"Portal\";case q:return\"Profiler\";case G:return\"StrictMode\";case T:return\"Suspense\";case D:return\"SuspenseList\"}if(typeof e==\"object\")switch(typeof e.tag==\"number\"&&console.error(\"Received an unexpected object in getComponentNameFromType(). This is likely a bug in React. Please file an issue.\"),e.$$typeof){case L:return(e.displayName||\"Context\")+\".Provider\";case K:return(e._context.displayName||\"Context\")+\".Consumer\";case A:var t=e.render;return e=e.displayName,e||(e=t.displayName||t.name||\"\",e=e!==\"\"?\"ForwardRef(\"+e+\")\":\"ForwardRef\"),e;case j:return t=e.displayName||null,t!==null?t:d(e.type)||\"Memo\";case O:t=e._payload,e=e._init;try{return d(e(t))}catch{}}return null}function n(e){return\"\"+e}function c(e){try{n(e);var t=!1}catch{t=!0}if(t){t=console;var o=t.error,i=typeof Symbol==\"function\"&&Symbol.toStringTag&&e[Symbol.toStringTag]||e.constructor.name||\"Object\";return o.call(t,\"The provided key is an unsupported type %s. This value must be coerced to a string before using it here.\",i),n(e)}}function y(){}function h(){if(x===0){J=console.log,Z=console.info,Q=console.warn,ee=console.error,ne=console.group,re=console.groupCollapsed,te=console.groupEnd;var e={configurable:!0,enumerable:!0,value:y,writable:!0};Object.defineProperties(console,{info:e,log:e,warn:e,error:e,group:e,groupCollapsed:e,groupEnd:e})}x++}function pe(){if(x--,x===0){var e={configurable:!0,enumerable:!0,writable:!0};Object.defineProperties(console,{log:_({},e,{value:J}),info:_({},e,{value:Z}),warn:_({},e,{value:Q}),error:_({},e,{value:ee}),group:_({},e,{value:ne}),groupCollapsed:_({},e,{value:re}),groupEnd:_({},e,{value:te})})}0>x&&console.error(\"disabledDepth fell below zero. This is a bug in React. Please file an issue.\")}function w(e){if(P===void 0)try{throw Error()}catch(o){var t=o.stack.trim().match(/\\n( *(at )?)/);P=t&&t[1]||\"\",oe=-1<o.stack.indexOf(`\n    at`)?\" (<anonymous>)\":-1<o.stack.indexOf(\"@\")?\"@unknown:0:0\":\"\"}return`\n`+P+e+oe}function $(e,t){if(!e||Y)return\"\";var o=I.get(e);if(o!==void 0)return o;Y=!0,o=Error.prepareStackTrace,Error.prepareStackTrace=void 0;var i=null;i=p.H,p.H=null,h();try{var u={DetermineComponentFrameRoot:function(){try{if(t){var b=function(){throw Error()};if(Object.defineProperty(b.prototype,\"props\",{set:function(){throw Error()}}),typeof Reflect==\"object\"&&Reflect.construct){try{Reflect.construct(b,[])}catch(m){var E=m}Reflect.construct(e,[],b)}else{try{b.call()}catch(m){E=m}e.call(b.prototype)}}else{try{throw Error()}catch(m){E=m}(b=e())&&typeof b.catch==\"function\"&&b.catch(function(){})}}catch(m){if(m&&E&&typeof m.stack==\"string\")return[m.stack,E.stack]}return[null,null]}};u.DetermineComponentFrameRoot.displayName=\"DetermineComponentFrameRoot\";var l=Object.getOwnPropertyDescriptor(u.DetermineComponentFrameRoot,\"name\");l&&l.configurable&&Object.defineProperty(u.DetermineComponentFrameRoot,\"name\",{value:\"DetermineComponentFrameRoot\"});var a=u.DetermineComponentFrameRoot(),f=a[0],g=a[1];if(f&&g){var s=f.split(`\n`),N=g.split(`\n`);for(a=l=0;l<s.length&&!s[l].includes(\"DetermineComponentFrameRoot\");)l++;for(;a<N.length&&!N[a].includes(\"DetermineComponentFrameRoot\");)a++;if(l===s.length||a===N.length)for(l=s.length-1,a=N.length-1;1<=l&&0<=a&&s[l]!==N[a];)a--;for(;1<=l&&0<=a;l--,a--)if(s[l]!==N[a]){if(l!==1||a!==1)do if(l--,a--,0>a||s[l]!==N[a]){var S=`\n`+s[l].replace(\" at new \",\" at \");return e.displayName&&S.includes(\"<anonymous>\")&&(S=S.replace(\"<anonymous>\",e.displayName)),typeof e==\"function\"&&I.set(e,S),S}while(1<=l&&0<=a);break}}}finally{Y=!1,p.H=i,pe(),Error.prepareStackTrace=o}return s=(s=e?e.displayName||e.name:\"\")?w(s):\"\",typeof e==\"function\"&&I.set(e,s),s}function v(e){if(e==null)return\"\";if(typeof e==\"function\"){var t=e.prototype;return $(e,!(!t||!t.isReactComponent))}if(typeof e==\"string\")return w(e);switch(e){case T:return w(\"Suspense\");case D:return w(\"SuspenseList\")}if(typeof e==\"object\")switch(e.$$typeof){case A:return e=$(e.render,!1),e;case j:return v(e.type);case O:t=e._payload,e=e._init;try{return v(e(t))}catch{}}return\"\"}function k(){var e=p.A;return e===null?null:e.getOwner()}function _e(e){if(F.call(e,\"key\")){var t=Object.getOwnPropertyDescriptor(e,\"key\").get;if(t&&t.isReactWarning)return!1}return e.key!==void 0}function Ne(e,t){function o(){ae||(ae=!0,console.error(\"%s: `key` is not a prop. Trying to access it will result in `undefined` being returned. If you need to access the same value within the child component, you should pass it as a different prop. (https://react.dev/link/special-props)\",t))}o.isReactWarning=!0,Object.defineProperty(e,\"key\",{get:o,configurable:!0})}function ge(){var e=d(this.type);return de[e]||(de[e]=!0,console.error(\"Accessing element.ref was removed in React 19. ref is now a regular prop. It will be removed from the JSX Element type in a future release.\")),e=this.props.ref,e!==void 0?e:null}function ye(e,t,o,i,u,l){return o=l.ref,e={$$typeof:V,type:e,key:t,props:l,_owner:u},(o!==void 0?o:null)!==null?Object.defineProperty(e,\"ref\",{enumerable:!1,get:ge}):Object.defineProperty(e,\"ref\",{enumerable:!1,value:null}),e._store={},Object.defineProperty(e._store,\"validated\",{configurable:!1,enumerable:!1,writable:!0,value:0}),Object.defineProperty(e,\"_debugInfo\",{configurable:!1,enumerable:!1,writable:!0,value:null}),Object.freeze&&(Object.freeze(e.props),Object.freeze(e)),e}function xe(e,t,o,i,u,l){if(typeof e==\"string\"||typeof e==\"function\"||e===z||e===q||e===G||e===T||e===D||e===Ce||typeof e==\"object\"&&e!==null&&(e.$$typeof===O||e.$$typeof===j||e.$$typeof===L||e.$$typeof===K||e.$$typeof===A||e.$$typeof===ke||e.getModuleId!==void 0)){var a=t.children;if(a!==void 0)if(i)if(R(a)){for(i=0;i<a.length;i++)B(a[i],e);Object.freeze&&Object.freeze(a)}else console.error(\"React.jsx: Static children should always be an array. You are likely explicitly calling React.jsxs or React.jsxDEV. Use the Babel transform instead.\");else B(a,e)}else a=\"\",(e===void 0||typeof e==\"object\"&&e!==null&&Object.keys(e).length===0)&&(a+=\" You likely forgot to export your component from the file it's defined in, or you might have mixed up default and named imports.\"),e===null?i=\"null\":R(e)?i=\"array\":e!==void 0&&e.$$typeof===V?(i=\"<\"+(d(e.type)||\"Unknown\")+\" />\",a=\" Did you accidentally export a JSX literal instead of a component?\"):i=typeof e,console.error(\"React.jsx: type is invalid -- expected a string (for built-in components) or a class/function (for composite components) but got: %s.%s\",i,a);if(F.call(t,\"key\")){a=d(e);var f=Object.keys(t).filter(function(s){return s!==\"key\"});i=0<f.length?\"{key: someKey, \"+f.join(\": ..., \")+\": ...}\":\"{key: someKey}\",ie[a+i]||(f=0<f.length?\"{\"+f.join(\": ..., \")+\": ...}\":\"{}\",console.error(`A props object containing a \"key\" prop is being spread into JSX:\n  let props = %s;\n  <%s {...props} />\nReact keys must be passed directly to JSX without using spread:\n  let props = %s;\n  <%s key={someKey} {...props} />`,i,a,f,a),ie[a+i]=!0)}if(a=null,o!==void 0&&(c(o),a=\"\"+o),_e(t)&&(c(t.key),a=\"\"+t.key),\"key\"in t){o={};for(var g in t)g!==\"key\"&&(o[g]=t[g])}else o=t;return a&&Ne(o,typeof e==\"function\"?e.displayName||e.name||\"Unknown\":e),ye(e,a,l,u,k(),o)}function B(e,t){if(typeof e==\"object\"&&e&&e.$$typeof!==We){if(R(e))for(var o=0;o<e.length;o++){var i=e[o];W(i)&&X(i,t)}else if(W(e))e._store&&(e._store.validated=1);else if(e===null||typeof e!=\"object\"?o=null:(o=H&&e[H]||e[\"@@iterator\"],o=typeof o==\"function\"?o:null),typeof o==\"function\"&&o!==e.entries&&(o=o.call(e),o!==e))for(;!(e=o.next()).done;)W(e.value)&&X(e.value,t)}}function W(e){return typeof e==\"object\"&&e!==null&&e.$$typeof===V}function X(e,t){if(e._store&&!e._store.validated&&e.key==null&&(e._store.validated=1,t=Se(t),!le[t])){le[t]=!0;var o=\"\";e&&e._owner!=null&&e._owner!==k()&&(o=null,typeof e._owner.tag==\"number\"?o=d(e._owner.type):typeof e._owner.name==\"string\"&&(o=e._owner.name),o=\" It was passed a child from \"+o+\".\");var i=p.getCurrentStack;p.getCurrentStack=function(){var u=v(e.type);return i&&(u+=i()||\"\"),u},console.error('Each child in a list should have a unique \"key\" prop.%s%s See https://react.dev/link/warning-keys for more information.',t,o),p.getCurrentStack=i}}function Se(e){var t=\"\",o=k();return o&&(o=d(o.type))&&(t=`\n\nCheck the render method of \\``+o+\"`.\"),t||(e=d(e))&&(t=`\n\nCheck the top-level render call using <`+e+\">.\"),t}var we=ce(),V=Symbol.for(\"react.transitional.element\"),Ee=Symbol.for(\"react.portal\"),z=Symbol.for(\"react.fragment\"),G=Symbol.for(\"react.strict_mode\"),q=Symbol.for(\"react.profiler\");Symbol.for(\"react.provider\");var K=Symbol.for(\"react.consumer\"),L=Symbol.for(\"react.context\"),A=Symbol.for(\"react.forward_ref\"),T=Symbol.for(\"react.suspense\"),D=Symbol.for(\"react.suspense_list\"),j=Symbol.for(\"react.memo\"),O=Symbol.for(\"react.lazy\"),Ce=Symbol.for(\"react.offscreen\"),H=Symbol.iterator,ve=Symbol.for(\"react.client.reference\"),p=we.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE,F=Object.prototype.hasOwnProperty,_=Object.assign,ke=Symbol.for(\"react.client.reference\"),R=Array.isArray,x=0,J,Z,Q,ee,ne,re,te;y.__reactDisabledLog=!0;var P,oe,Y=!1,I=new(typeof WeakMap==\"function\"?WeakMap:Map),We=Symbol.for(\"react.client.reference\"),ae,de={},ie={},le={};U.Fragment=z,U.jsxDEV=function(e,t,o,i,u,l){return xe(e,t,o,i,u,l)}})()});var be=M((Xe,me)=>{\"use strict\";me.exports=fe()});var Me={};je(Me,{default:()=>Ie,frontmatter:()=>Pe});var r=Oe(be()),Pe={title:\"ShipAny \\u5FEB\\u901F\\u5165\\u95E8\\uFF1A\\u51E0\\u5C0F\\u65F6\\u5185\\u6784\\u5EFA\\u4F60\\u7684 AI SaaS\",slug:\"getting-started-with-shipany\",description:\"\\u5B66\\u4E60\\u5982\\u4F55\\u4F7F\\u7528 ShipAny \\u5F3A\\u5927\\u7684\\u6A21\\u677F\\u548C\\u7EC4\\u4EF6\\u5FEB\\u901F\\u6784\\u5EFA\\u548C\\u90E8\\u7F72\\u4F60\\u7684 AI SaaS \\u5E94\\u7528\\u7A0B\\u5E8F\\u3002\",coverImage:\"https://images.unsplash.com/photo-1551288049-bebda4e38f71?w=800&h=400&fit=crop\",author:\"ShipAny \\u56E2\\u961F\",authorImage:\"https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=100&h=100&fit=crop&crop=face\",publishedAt:\"2025-01-17\",featured:!0,tags:[\"\\u6559\\u7A0B\",\"\\u5FEB\\u901F\\u5165\\u95E8\",\"ai-saas\"]};function he(d){let n=Object.assign({h1:\"h1\",p:\"p\",h2:\"h2\",ul:\"ul\",li:\"li\",strong:\"strong\",h3:\"h3\",pre:\"pre\",code:\"code\",ol:\"ol\"},d.components);return(0,r.jsxDEV)(r.Fragment,{children:[(0,r.jsxDEV)(n.h1,{children:\"ShipAny \\u5FEB\\u901F\\u5165\\u95E8\"},void 0,!1,{fileName:\"D:\\\\VSCodeWorkspace\\\\shipany-stwd\\\\content\\\\blogs\\\\zh\\\\_mdx_bundler_entry_point-4d5c7a7e-9e8b-4023-8589-1da00ff8d7ea.mdx\",lineNumber:13,columnNumber:1},this),`\n`,(0,r.jsxDEV)(n.p,{children:\"\\u6B22\\u8FCE\\u4F7F\\u7528 ShipAny\\uFF01\\u8FD9\\u4EFD\\u7EFC\\u5408\\u6307\\u5357\\u5C06\\u5E26\\u4F60\\u4F7F\\u7528\\u6211\\u4EEC\\u5F3A\\u5927\\u7684\\u6A21\\u677F\\u7CFB\\u7EDF\\u6784\\u5EFA\\u4F60\\u7684\\u7B2C\\u4E00\\u4E2A AI SaaS \\u5E94\\u7528\\u7A0B\\u5E8F\\u3002\"},void 0,!1,{fileName:\"D:\\\\VSCodeWorkspace\\\\shipany-stwd\\\\content\\\\blogs\\\\zh\\\\_mdx_bundler_entry_point-4d5c7a7e-9e8b-4023-8589-1da00ff8d7ea.mdx\",lineNumber:15,columnNumber:1},this),`\n`,(0,r.jsxDEV)(n.h2,{children:\"\\u4EC0\\u4E48\\u662F ShipAny\\uFF1F\"},void 0,!1,{fileName:\"D:\\\\VSCodeWorkspace\\\\shipany-stwd\\\\content\\\\blogs\\\\zh\\\\_mdx_bundler_entry_point-4d5c7a7e-9e8b-4023-8589-1da00ff8d7ea.mdx\",lineNumber:17,columnNumber:1},this),`\n`,(0,r.jsxDEV)(n.p,{children:\"ShipAny \\u662F\\u4E00\\u4E2A\\u4E13\\u4E3A\\u5FEB\\u901F\\u9AD8\\u6548\\u6784\\u5EFA AI SaaS \\u521B\\u4E1A\\u9879\\u76EE\\u800C\\u8BBE\\u8BA1\\u7684 Next.js \\u6837\\u677F\\u3002\\u901A\\u8FC7\\u9884\\u6784\\u5EFA\\u7684\\u7EC4\\u4EF6\\u3001\\u8EAB\\u4EFD\\u9A8C\\u8BC1\\u3001\\u652F\\u4ED8\\u5904\\u7406\\u548C AI \\u96C6\\u6210\\uFF0C\\u4F60\\u53EF\\u4EE5\\u4E13\\u6CE8\\u4E8E\\u4F60\\u7684\\u72EC\\u7279\\u4EF7\\u503C\\u4E3B\\u5F20\\uFF0C\\u800C\\u4E0D\\u662F\\u6837\\u677F\\u4EE3\\u7801\\u3002\"},void 0,!1,{fileName:\"D:\\\\VSCodeWorkspace\\\\shipany-stwd\\\\content\\\\blogs\\\\zh\\\\_mdx_bundler_entry_point-4d5c7a7e-9e8b-4023-8589-1da00ff8d7ea.mdx\",lineNumber:19,columnNumber:1},this),`\n`,(0,r.jsxDEV)(n.h2,{children:\"\\u6838\\u5FC3\\u7279\\u6027\"},void 0,!1,{fileName:\"D:\\\\VSCodeWorkspace\\\\shipany-stwd\\\\content\\\\blogs\\\\zh\\\\_mdx_bundler_entry_point-4d5c7a7e-9e8b-4023-8589-1da00ff8d7ea.mdx\",lineNumber:21,columnNumber:1},this),`\n`,(0,r.jsxDEV)(n.ul,{children:[`\n`,(0,r.jsxDEV)(n.li,{children:[(0,r.jsxDEV)(n.strong,{children:\"\\u{1F680} \\u5FEB\\u901F\\u5F00\\u53D1\"},void 0,!1,{fileName:\"D:\\\\VSCodeWorkspace\\\\shipany-stwd\\\\content\\\\blogs\\\\zh\\\\_mdx_bundler_entry_point-4d5c7a7e-9e8b-4023-8589-1da00ff8d7ea.mdx\",lineNumber:23,columnNumber:3},this),\"\\uFF1A\\u9884\\u6784\\u5EFA\\u7684\\u7EC4\\u4EF6\\u548C\\u6A21\\u677F\"]},void 0,!0,{fileName:\"D:\\\\VSCodeWorkspace\\\\shipany-stwd\\\\content\\\\blogs\\\\zh\\\\_mdx_bundler_entry_point-4d5c7a7e-9e8b-4023-8589-1da00ff8d7ea.mdx\",lineNumber:23,columnNumber:1},this),`\n`,(0,r.jsxDEV)(n.li,{children:[(0,r.jsxDEV)(n.strong,{children:\"\\u{1F916} AI \\u96C6\\u6210\"},void 0,!1,{fileName:\"D:\\\\VSCodeWorkspace\\\\shipany-stwd\\\\content\\\\blogs\\\\zh\\\\_mdx_bundler_entry_point-4d5c7a7e-9e8b-4023-8589-1da00ff8d7ea.mdx\",lineNumber:24,columnNumber:3},this),\"\\uFF1A\\u5F00\\u7BB1\\u5373\\u7528\\u7684 AI SDK \\u96C6\\u6210\"]},void 0,!0,{fileName:\"D:\\\\VSCodeWorkspace\\\\shipany-stwd\\\\content\\\\blogs\\\\zh\\\\_mdx_bundler_entry_point-4d5c7a7e-9e8b-4023-8589-1da00ff8d7ea.mdx\",lineNumber:24,columnNumber:1},this),`\n`,(0,r.jsxDEV)(n.li,{children:[(0,r.jsxDEV)(n.strong,{children:\"\\u{1F4B3} \\u652F\\u4ED8\\u5904\\u7406\"},void 0,!1,{fileName:\"D:\\\\VSCodeWorkspace\\\\shipany-stwd\\\\content\\\\blogs\\\\zh\\\\_mdx_bundler_entry_point-4d5c7a7e-9e8b-4023-8589-1da00ff8d7ea.mdx\",lineNumber:25,columnNumber:3},this),\"\\uFF1A\\u5185\\u7F6E Stripe \\u96C6\\u6210\"]},void 0,!0,{fileName:\"D:\\\\VSCodeWorkspace\\\\shipany-stwd\\\\content\\\\blogs\\\\zh\\\\_mdx_bundler_entry_point-4d5c7a7e-9e8b-4023-8589-1da00ff8d7ea.mdx\",lineNumber:25,columnNumber:1},this),`\n`,(0,r.jsxDEV)(n.li,{children:[(0,r.jsxDEV)(n.strong,{children:\"\\u{1F30D} \\u56FD\\u9645\\u5316\"},void 0,!1,{fileName:\"D:\\\\VSCodeWorkspace\\\\shipany-stwd\\\\content\\\\blogs\\\\zh\\\\_mdx_bundler_entry_point-4d5c7a7e-9e8b-4023-8589-1da00ff8d7ea.mdx\",lineNumber:26,columnNumber:3},this),\"\\uFF1A\\u591A\\u8BED\\u8A00\\u652F\\u6301\"]},void 0,!0,{fileName:\"D:\\\\VSCodeWorkspace\\\\shipany-stwd\\\\content\\\\blogs\\\\zh\\\\_mdx_bundler_entry_point-4d5c7a7e-9e8b-4023-8589-1da00ff8d7ea.mdx\",lineNumber:26,columnNumber:1},this),`\n`,(0,r.jsxDEV)(n.li,{children:[(0,r.jsxDEV)(n.strong,{children:\"\\u{1F4F1} \\u54CD\\u5E94\\u5F0F\\u8BBE\\u8BA1\"},void 0,!1,{fileName:\"D:\\\\VSCodeWorkspace\\\\shipany-stwd\\\\content\\\\blogs\\\\zh\\\\_mdx_bundler_entry_point-4d5c7a7e-9e8b-4023-8589-1da00ff8d7ea.mdx\",lineNumber:27,columnNumber:3},this),\"\\uFF1A\\u79FB\\u52A8\\u4F18\\u5148\\u7684\\u65B9\\u6CD5\"]},void 0,!0,{fileName:\"D:\\\\VSCodeWorkspace\\\\shipany-stwd\\\\content\\\\blogs\\\\zh\\\\_mdx_bundler_entry_point-4d5c7a7e-9e8b-4023-8589-1da00ff8d7ea.mdx\",lineNumber:27,columnNumber:1},this),`\n`,(0,r.jsxDEV)(n.li,{children:[(0,r.jsxDEV)(n.strong,{children:\"\\u{1F510} \\u8EAB\\u4EFD\\u9A8C\\u8BC1\"},void 0,!1,{fileName:\"D:\\\\VSCodeWorkspace\\\\shipany-stwd\\\\content\\\\blogs\\\\zh\\\\_mdx_bundler_entry_point-4d5c7a7e-9e8b-4023-8589-1da00ff8d7ea.mdx\",lineNumber:28,columnNumber:3},this),\"\\uFF1A\\u5B89\\u5168\\u7684\\u7528\\u6237\\u7BA1\\u7406\"]},void 0,!0,{fileName:\"D:\\\\VSCodeWorkspace\\\\shipany-stwd\\\\content\\\\blogs\\\\zh\\\\_mdx_bundler_entry_point-4d5c7a7e-9e8b-4023-8589-1da00ff8d7ea.mdx\",lineNumber:28,columnNumber:1},this),`\n`]},void 0,!0,{fileName:\"D:\\\\VSCodeWorkspace\\\\shipany-stwd\\\\content\\\\blogs\\\\zh\\\\_mdx_bundler_entry_point-4d5c7a7e-9e8b-4023-8589-1da00ff8d7ea.mdx\",lineNumber:23,columnNumber:1},this),`\n`,(0,r.jsxDEV)(n.h2,{children:\"\\u5FEB\\u901F\\u5F00\\u59CB\"},void 0,!1,{fileName:\"D:\\\\VSCodeWorkspace\\\\shipany-stwd\\\\content\\\\blogs\\\\zh\\\\_mdx_bundler_entry_point-4d5c7a7e-9e8b-4023-8589-1da00ff8d7ea.mdx\",lineNumber:30,columnNumber:1},this),`\n`,(0,r.jsxDEV)(n.h3,{children:\"1. \\u514B\\u9686\\u4ED3\\u5E93\"},void 0,!1,{fileName:\"D:\\\\VSCodeWorkspace\\\\shipany-stwd\\\\content\\\\blogs\\\\zh\\\\_mdx_bundler_entry_point-4d5c7a7e-9e8b-4023-8589-1da00ff8d7ea.mdx\",lineNumber:32,columnNumber:1},this),`\n`,(0,r.jsxDEV)(n.pre,{children:(0,r.jsxDEV)(n.code,{className:\"language-bash\",children:`git clone https://github.com/shipany/shipany-template\ncd shipany-template\n`},void 0,!1,{fileName:\"D:\\\\VSCodeWorkspace\\\\shipany-stwd\\\\content\\\\blogs\\\\zh\\\\_mdx_bundler_entry_point-4d5c7a7e-9e8b-4023-8589-1da00ff8d7ea.mdx\",lineNumber:34,columnNumber:1},this)},void 0,!1,{fileName:\"D:\\\\VSCodeWorkspace\\\\shipany-stwd\\\\content\\\\blogs\\\\zh\\\\_mdx_bundler_entry_point-4d5c7a7e-9e8b-4023-8589-1da00ff8d7ea.mdx\",lineNumber:34,columnNumber:1},this),`\n`,(0,r.jsxDEV)(n.h3,{children:\"2. \\u5B89\\u88C5\\u4F9D\\u8D56\"},void 0,!1,{fileName:\"D:\\\\VSCodeWorkspace\\\\shipany-stwd\\\\content\\\\blogs\\\\zh\\\\_mdx_bundler_entry_point-4d5c7a7e-9e8b-4023-8589-1da00ff8d7ea.mdx\",lineNumber:39,columnNumber:1},this),`\n`,(0,r.jsxDEV)(n.pre,{children:(0,r.jsxDEV)(n.code,{className:\"language-bash\",children:`pnpm install\n`},void 0,!1,{fileName:\"D:\\\\VSCodeWorkspace\\\\shipany-stwd\\\\content\\\\blogs\\\\zh\\\\_mdx_bundler_entry_point-4d5c7a7e-9e8b-4023-8589-1da00ff8d7ea.mdx\",lineNumber:41,columnNumber:1},this)},void 0,!1,{fileName:\"D:\\\\VSCodeWorkspace\\\\shipany-stwd\\\\content\\\\blogs\\\\zh\\\\_mdx_bundler_entry_point-4d5c7a7e-9e8b-4023-8589-1da00ff8d7ea.mdx\",lineNumber:41,columnNumber:1},this),`\n`,(0,r.jsxDEV)(n.h3,{children:\"3. \\u8BBE\\u7F6E\\u73AF\\u5883\\u53D8\\u91CF\"},void 0,!1,{fileName:\"D:\\\\VSCodeWorkspace\\\\shipany-stwd\\\\content\\\\blogs\\\\zh\\\\_mdx_bundler_entry_point-4d5c7a7e-9e8b-4023-8589-1da00ff8d7ea.mdx\",lineNumber:45,columnNumber:1},this),`\n`,(0,r.jsxDEV)(n.p,{children:[\"\\u521B\\u5EFA\\u4E00\\u4E2A \",(0,r.jsxDEV)(n.code,{children:\".env.local\"},void 0,!1,{fileName:\"D:\\\\VSCodeWorkspace\\\\shipany-stwd\\\\content\\\\blogs\\\\zh\\\\_mdx_bundler_entry_point-4d5c7a7e-9e8b-4023-8589-1da00ff8d7ea.mdx\",lineNumber:47,columnNumber:6},this),\" \\u6587\\u4EF6\\u5E76\\u914D\\u7F6E\\uFF1A\"]},void 0,!0,{fileName:\"D:\\\\VSCodeWorkspace\\\\shipany-stwd\\\\content\\\\blogs\\\\zh\\\\_mdx_bundler_entry_point-4d5c7a7e-9e8b-4023-8589-1da00ff8d7ea.mdx\",lineNumber:47,columnNumber:1},this),`\n`,(0,r.jsxDEV)(n.pre,{children:(0,r.jsxDEV)(n.code,{className:\"language-env\",children:`NEXT_PUBLIC_WEB_URL=http://localhost:3000\nDATABASE_URL=your_database_url\nNEXTAUTH_SECRET=your_secret\nSTRIPE_SECRET_KEY=your_stripe_key\n`},void 0,!1,{fileName:\"D:\\\\VSCodeWorkspace\\\\shipany-stwd\\\\content\\\\blogs\\\\zh\\\\_mdx_bundler_entry_point-4d5c7a7e-9e8b-4023-8589-1da00ff8d7ea.mdx\",lineNumber:49,columnNumber:1},this)},void 0,!1,{fileName:\"D:\\\\VSCodeWorkspace\\\\shipany-stwd\\\\content\\\\blogs\\\\zh\\\\_mdx_bundler_entry_point-4d5c7a7e-9e8b-4023-8589-1da00ff8d7ea.mdx\",lineNumber:49,columnNumber:1},this),`\n`,(0,r.jsxDEV)(n.h3,{children:\"4. \\u8FD0\\u884C\\u5F00\\u53D1\\u670D\\u52A1\\u5668\"},void 0,!1,{fileName:\"D:\\\\VSCodeWorkspace\\\\shipany-stwd\\\\content\\\\blogs\\\\zh\\\\_mdx_bundler_entry_point-4d5c7a7e-9e8b-4023-8589-1da00ff8d7ea.mdx\",lineNumber:56,columnNumber:1},this),`\n`,(0,r.jsxDEV)(n.pre,{children:(0,r.jsxDEV)(n.code,{className:\"language-bash\",children:`pnpm dev\n`},void 0,!1,{fileName:\"D:\\\\VSCodeWorkspace\\\\shipany-stwd\\\\content\\\\blogs\\\\zh\\\\_mdx_bundler_entry_point-4d5c7a7e-9e8b-4023-8589-1da00ff8d7ea.mdx\",lineNumber:58,columnNumber:1},this)},void 0,!1,{fileName:\"D:\\\\VSCodeWorkspace\\\\shipany-stwd\\\\content\\\\blogs\\\\zh\\\\_mdx_bundler_entry_point-4d5c7a7e-9e8b-4023-8589-1da00ff8d7ea.mdx\",lineNumber:58,columnNumber:1},this),`\n`,(0,r.jsxDEV)(n.h2,{children:\"\\u6784\\u5EFA\\u4F60\\u7684\\u7B2C\\u4E00\\u4E2A\\u529F\\u80FD\"},void 0,!1,{fileName:\"D:\\\\VSCodeWorkspace\\\\shipany-stwd\\\\content\\\\blogs\\\\zh\\\\_mdx_bundler_entry_point-4d5c7a7e-9e8b-4023-8589-1da00ff8d7ea.mdx\",lineNumber:62,columnNumber:1},this),`\n`,(0,r.jsxDEV)(n.p,{children:\"\\u8BA9\\u6211\\u4EEC\\u521B\\u5EFA\\u4E00\\u4E2A\\u7B80\\u5355\\u7684 AI \\u9A71\\u52A8\\u7684\\u6587\\u672C\\u751F\\u6210\\u5668\\uFF1A\"},void 0,!1,{fileName:\"D:\\\\VSCodeWorkspace\\\\shipany-stwd\\\\content\\\\blogs\\\\zh\\\\_mdx_bundler_entry_point-4d5c7a7e-9e8b-4023-8589-1da00ff8d7ea.mdx\",lineNumber:64,columnNumber:1},this),`\n`,(0,r.jsxDEV)(n.h3,{children:\"1. \\u521B\\u5EFA API \\u8DEF\\u7531\"},void 0,!1,{fileName:\"D:\\\\VSCodeWorkspace\\\\shipany-stwd\\\\content\\\\blogs\\\\zh\\\\_mdx_bundler_entry_point-4d5c7a7e-9e8b-4023-8589-1da00ff8d7ea.mdx\",lineNumber:66,columnNumber:1},this),`\n`,(0,r.jsxDEV)(n.pre,{children:(0,r.jsxDEV)(n.code,{className:\"language-typescript\",children:`// app/api/generate/route.ts\nimport { openai } from '@ai-sdk/openai'\nimport { generateText } from 'ai'\n\nexport async function POST(request: Request) {\n  const { prompt } = await request.json()\n  \n  const { text } = await generateText({\n    model: openai('gpt-3.5-turbo'),\n    prompt: \\`\\u57FA\\u4E8E\\u4EE5\\u4E0B\\u5185\\u5BB9\\u751F\\u6210\\u521B\\u610F\\u6587\\u672C\\uFF1A\\${prompt}\\`,\n  })\n  \n  return Response.json({ text })\n}\n`},void 0,!1,{fileName:\"D:\\\\VSCodeWorkspace\\\\shipany-stwd\\\\content\\\\blogs\\\\zh\\\\_mdx_bundler_entry_point-4d5c7a7e-9e8b-4023-8589-1da00ff8d7ea.mdx\",lineNumber:68,columnNumber:1},this)},void 0,!1,{fileName:\"D:\\\\VSCodeWorkspace\\\\shipany-stwd\\\\content\\\\blogs\\\\zh\\\\_mdx_bundler_entry_point-4d5c7a7e-9e8b-4023-8589-1da00ff8d7ea.mdx\",lineNumber:68,columnNumber:1},this),`\n`,(0,r.jsxDEV)(n.h3,{children:\"2. \\u521B\\u5EFA\\u524D\\u7AEF\\u7EC4\\u4EF6\"},void 0,!1,{fileName:\"D:\\\\VSCodeWorkspace\\\\shipany-stwd\\\\content\\\\blogs\\\\zh\\\\_mdx_bundler_entry_point-4d5c7a7e-9e8b-4023-8589-1da00ff8d7ea.mdx\",lineNumber:85,columnNumber:1},this),`\n`,(0,r.jsxDEV)(n.pre,{children:(0,r.jsxDEV)(n.code,{className:\"language-tsx\",children:`'use client'\n\nimport { useState } from 'react'\nimport { Button } from '@/components/ui/button'\nimport { Textarea } from '@/components/ui/textarea'\n\nexport function TextGenerator() {\n  const [prompt, setPrompt] = useState('')\n  const [result, setResult] = useState('')\n  const [loading, setLoading] = useState(false)\n\n  const handleGenerate = async () => {\n    setLoading(true)\n    try {\n      const response = await fetch('/api/generate', {\n        method: 'POST',\n        headers: { 'Content-Type': 'application/json' },\n        body: JSON.stringify({ prompt }),\n      })\n      const data = await response.json()\n      setResult(data.text)\n    } catch (error) {\n      console.error('\\u9519\\u8BEF:', error)\n    } finally {\n      setLoading(false)\n    }\n  }\n\n  return (\n    <div className=\"space-y-4\">\n      <Textarea\n        placeholder=\"\\u8F93\\u5165\\u4F60\\u7684\\u63D0\\u793A...\"\n        value={prompt}\n        onChange={(e) => setPrompt(e.target.value)}\n      />\n      <Button onClick={handleGenerate} disabled={loading}>\n        {loading ? '\\u751F\\u6210\\u4E2D...' : '\\u751F\\u6210'}\n      </Button>\n      {result && (\n        <div className=\"p-4 bg-muted rounded-lg\">\n          {result}\n        </div>\n      )}\n    </div>\n  )\n}\n`},void 0,!1,{fileName:\"D:\\\\VSCodeWorkspace\\\\shipany-stwd\\\\content\\\\blogs\\\\zh\\\\_mdx_bundler_entry_point-4d5c7a7e-9e8b-4023-8589-1da00ff8d7ea.mdx\",lineNumber:87,columnNumber:1},this)},void 0,!1,{fileName:\"D:\\\\VSCodeWorkspace\\\\shipany-stwd\\\\content\\\\blogs\\\\zh\\\\_mdx_bundler_entry_point-4d5c7a7e-9e8b-4023-8589-1da00ff8d7ea.mdx\",lineNumber:87,columnNumber:1},this),`\n`,(0,r.jsxDEV)(n.h2,{children:\"\\u4E0B\\u4E00\\u6B65\"},void 0,!1,{fileName:\"D:\\\\VSCodeWorkspace\\\\shipany-stwd\\\\content\\\\blogs\\\\zh\\\\_mdx_bundler_entry_point-4d5c7a7e-9e8b-4023-8589-1da00ff8d7ea.mdx\",lineNumber:136,columnNumber:1},this),`\n`,(0,r.jsxDEV)(n.p,{children:\"\\u73B0\\u5728\\u4F60\\u5DF2\\u7ECF\\u8BBE\\u7F6E\\u597D\\u4E86\\u57FA\\u7840\\uFF0C\\u4F60\\u53EF\\u4EE5\\uFF1A\"},void 0,!1,{fileName:\"D:\\\\VSCodeWorkspace\\\\shipany-stwd\\\\content\\\\blogs\\\\zh\\\\_mdx_bundler_entry_point-4d5c7a7e-9e8b-4023-8589-1da00ff8d7ea.mdx\",lineNumber:138,columnNumber:1},this),`\n`,(0,r.jsxDEV)(n.ol,{children:[`\n`,(0,r.jsxDEV)(n.li,{children:[(0,r.jsxDEV)(n.strong,{children:\"\\u81EA\\u5B9A\\u4E49 UI\"},void 0,!1,{fileName:\"D:\\\\VSCodeWorkspace\\\\shipany-stwd\\\\content\\\\blogs\\\\zh\\\\_mdx_bundler_entry_point-4d5c7a7e-9e8b-4023-8589-1da00ff8d7ea.mdx\",lineNumber:140,columnNumber:4},this),\"\\uFF1A\\u4FEE\\u6539\\u7EC4\\u4EF6\\u4EE5\\u5339\\u914D\\u4F60\\u7684\\u54C1\\u724C\"]},void 0,!0,{fileName:\"D:\\\\VSCodeWorkspace\\\\shipany-stwd\\\\content\\\\blogs\\\\zh\\\\_mdx_bundler_entry_point-4d5c7a7e-9e8b-4023-8589-1da00ff8d7ea.mdx\",lineNumber:140,columnNumber:1},this),`\n`,(0,r.jsxDEV)(n.li,{children:[(0,r.jsxDEV)(n.strong,{children:\"\\u6DFB\\u52A0\\u66F4\\u591A AI \\u529F\\u80FD\"},void 0,!1,{fileName:\"D:\\\\VSCodeWorkspace\\\\shipany-stwd\\\\content\\\\blogs\\\\zh\\\\_mdx_bundler_entry_point-4d5c7a7e-9e8b-4023-8589-1da00ff8d7ea.mdx\",lineNumber:141,columnNumber:4},this),\"\\uFF1A\\u96C6\\u6210\\u989D\\u5916\\u7684 AI \\u6A21\\u578B\"]},void 0,!0,{fileName:\"D:\\\\VSCodeWorkspace\\\\shipany-stwd\\\\content\\\\blogs\\\\zh\\\\_mdx_bundler_entry_point-4d5c7a7e-9e8b-4023-8589-1da00ff8d7ea.mdx\",lineNumber:141,columnNumber:1},this),`\n`,(0,r.jsxDEV)(n.li,{children:[(0,r.jsxDEV)(n.strong,{children:\"\\u8BBE\\u7F6E\\u652F\\u4ED8\"},void 0,!1,{fileName:\"D:\\\\VSCodeWorkspace\\\\shipany-stwd\\\\content\\\\blogs\\\\zh\\\\_mdx_bundler_entry_point-4d5c7a7e-9e8b-4023-8589-1da00ff8d7ea.mdx\",lineNumber:142,columnNumber:4},this),\"\\uFF1A\\u4E3A\\u8BA2\\u9605\\u914D\\u7F6E Stripe\"]},void 0,!0,{fileName:\"D:\\\\VSCodeWorkspace\\\\shipany-stwd\\\\content\\\\blogs\\\\zh\\\\_mdx_bundler_entry_point-4d5c7a7e-9e8b-4023-8589-1da00ff8d7ea.mdx\",lineNumber:142,columnNumber:1},this),`\n`,(0,r.jsxDEV)(n.li,{children:[(0,r.jsxDEV)(n.strong,{children:\"\\u90E8\\u7F72\"},void 0,!1,{fileName:\"D:\\\\VSCodeWorkspace\\\\shipany-stwd\\\\content\\\\blogs\\\\zh\\\\_mdx_bundler_entry_point-4d5c7a7e-9e8b-4023-8589-1da00ff8d7ea.mdx\",lineNumber:143,columnNumber:4},this),\"\\uFF1A\\u90E8\\u7F72\\u5230 Vercel \\u6216\\u4F60\\u9996\\u9009\\u7684\\u5E73\\u53F0\"]},void 0,!0,{fileName:\"D:\\\\VSCodeWorkspace\\\\shipany-stwd\\\\content\\\\blogs\\\\zh\\\\_mdx_bundler_entry_point-4d5c7a7e-9e8b-4023-8589-1da00ff8d7ea.mdx\",lineNumber:143,columnNumber:1},this),`\n`]},void 0,!0,{fileName:\"D:\\\\VSCodeWorkspace\\\\shipany-stwd\\\\content\\\\blogs\\\\zh\\\\_mdx_bundler_entry_point-4d5c7a7e-9e8b-4023-8589-1da00ff8d7ea.mdx\",lineNumber:140,columnNumber:1},this),`\n`,(0,r.jsxDEV)(n.h2,{children:\"\\u7ED3\\u8BBA\"},void 0,!1,{fileName:\"D:\\\\VSCodeWorkspace\\\\shipany-stwd\\\\content\\\\blogs\\\\zh\\\\_mdx_bundler_entry_point-4d5c7a7e-9e8b-4023-8589-1da00ff8d7ea.mdx\",lineNumber:145,columnNumber:1},this),`\n`,(0,r.jsxDEV)(n.p,{children:\"ShipAny \\u63D0\\u4F9B\\u4E86\\u5FEB\\u901F\\u6784\\u5EFA\\u548C\\u542F\\u52A8 AI SaaS \\u6240\\u9700\\u7684\\u4E00\\u5207\\u3002\\u51ED\\u501F\\u5176\\u5168\\u9762\\u7684\\u529F\\u80FD\\u96C6\\u548C\\u5F00\\u53D1\\u8005\\u53CB\\u597D\\u7684\\u67B6\\u6784\\uFF0C\\u4F60\\u53EF\\u4EE5\\u4E13\\u6CE8\\u4E8E\\u6700\\u91CD\\u8981\\u7684\\u4E8B\\u60C5\\uFF1A\\u4E3A\\u7528\\u6237\\u6784\\u5EFA\\u4F18\\u79C0\\u7684\\u4EA7\\u54C1\\u3002\"},void 0,!1,{fileName:\"D:\\\\VSCodeWorkspace\\\\shipany-stwd\\\\content\\\\blogs\\\\zh\\\\_mdx_bundler_entry_point-4d5c7a7e-9e8b-4023-8589-1da00ff8d7ea.mdx\",lineNumber:147,columnNumber:1},this),`\n`,(0,r.jsxDEV)(n.p,{children:\"\\u51C6\\u5907\\u597D\\u53D1\\u5E03\\u4F60\\u7684\\u4E0B\\u4E00\\u4E2A AI SaaS \\u4E86\\u5417\\uFF1F\\u4ECA\\u5929\\u5C31\\u5F00\\u59CB\\u4F7F\\u7528 ShipAny \\u5427\\uFF01\"},void 0,!1,{fileName:\"D:\\\\VSCodeWorkspace\\\\shipany-stwd\\\\content\\\\blogs\\\\zh\\\\_mdx_bundler_entry_point-4d5c7a7e-9e8b-4023-8589-1da00ff8d7ea.mdx\",lineNumber:149,columnNumber:1},this)]},void 0,!0,{fileName:\"D:\\\\VSCodeWorkspace\\\\shipany-stwd\\\\content\\\\blogs\\\\zh\\\\_mdx_bundler_entry_point-4d5c7a7e-9e8b-4023-8589-1da00ff8d7ea.mdx\",lineNumber:1,columnNumber:1},this)}function Ye(d={}){let{wrapper:n}=d.components||{};return n?(0,r.jsxDEV)(n,Object.assign({},d,{children:(0,r.jsxDEV)(he,d,void 0,!1,{fileName:\"D:\\\\VSCodeWorkspace\\\\shipany-stwd\\\\content\\\\blogs\\\\zh\\\\_mdx_bundler_entry_point-4d5c7a7e-9e8b-4023-8589-1da00ff8d7ea.mdx\"},this)}),void 0,!1,{fileName:\"D:\\\\VSCodeWorkspace\\\\shipany-stwd\\\\content\\\\blogs\\\\zh\\\\_mdx_bundler_entry_point-4d5c7a7e-9e8b-4023-8589-1da00ff8d7ea.mdx\"},this):he(d)}var Ie=Ye;return Re(Me);})();\n/*! Bundled license information:\n\nreact/cjs/react-jsx-dev-runtime.development.js:\n  (**\n   * @license React\n   * react-jsx-dev-runtime.development.js\n   *\n   * Copyright (c) Meta Platforms, Inc. and affiliates.\n   *\n   * This source code is licensed under the MIT license found in the\n   * LICENSE file in the root directory of this source tree.\n   *)\n*/\n;return Component;"}, "_id": "blogs/zh/shipany-kuai-su-ru-men.mdx", "_raw": {"sourceFilePath": "blogs/zh/shipany-kuai-su-ru-men.mdx", "sourceFileName": "shipany-kuai-su-ru-men.mdx", "sourceFileDir": "blogs/zh", "contentType": "mdx", "flattenedPath": "blogs/zh/shipany-kuai-su-ru-men"}, "type": "Blog", "lang": "zh", "url": "/blogs/zh/getting-started-with-shipany", "createdAt": "2025-07-17T08:59:48.408Z"}