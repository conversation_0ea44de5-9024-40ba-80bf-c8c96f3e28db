"use client";

import { useParams, usePathname, useRouter } from "next/navigation";
import { toast } from "sonner";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { MdLanguage, MdCheck, MdClose } from "react-icons/md";
import { localeNames, locales } from "@/i18n/locale";
import { 
  detectContentPage, 
  getAvailableLanguageVersions, 
  handleContentLanguageSwitch,
  getContentTitle 
} from "@/lib/content-language-utils";

interface ContentLanguageIndicatorProps {
  className?: string;
  variant?: 'compact' | 'full';
}

export default function ContentLanguageIndicator({ 
  className = "", 
  variant = 'compact' 
}: ContentLanguageIndicatorProps) {
  const params = useParams();
  const locale = params.locale as string;
  const router = useRouter();
  const pathname = usePathname();

  // Detect if this is a content page
  const contentInfo = detectContentPage(pathname, locale);
  
  // Only show on content pages with a specific slug
  if (contentInfo.type === 'other' || !contentInfo.slug) {
    return null;
  }

  // Get available language versions
  const languageVersions = getAvailableLanguageVersions(
    contentInfo.type,
    contentInfo.slug,
    locales
  );

  // Get current content title
  const currentTitle = getContentTitle(contentInfo.type, contentInfo.slug, locale);

  const handleLanguageSwitch = (targetLocale: string) => {
    if (targetLocale !== locale) {
      const switchResult = handleContentLanguageSwitch(
        pathname,
        locale,
        targetLocale,
        locales
      );

      router.push(switchResult.url);

      // Show user feedback for fallback scenarios
      if (switchResult.strategy === 'fallback-list') {
        toast.info(
          `This content is not available in ${localeNames[targetLocale]}. Redirected to the content list.`,
          {
            duration: 4000,
          }
        );
      } else if (switchResult.strategy === 'fallback-home') {
        toast.info(
          `Redirected to ${localeNames[targetLocale]} homepage.`,
          {
            duration: 3000,
          }
        );
      }
    }
  };

  if (variant === 'compact') {
    return (
      <div className={`flex items-center gap-2 ${className}`}>
        <MdLanguage className="text-muted-foreground" size={16} />
        <div className="flex items-center gap-1">
          {languageVersions.map((version) => (
            <Button
              key={version.locale}
              variant={version.locale === locale ? "default" : "outline"}
              size="sm"
              className="h-6 px-2 text-xs"
              onClick={() => handleLanguageSwitch(version.locale)}
              disabled={!version.exists && version.locale !== locale}
            >
              <span className="flex items-center gap-1">
                {localeNames[version.locale]}
                {version.exists ? (
                  <MdCheck size={12} className="text-green-500" />
                ) : (
                  <MdClose size={12} className="text-red-500" />
                )}
              </span>
            </Button>
          ))}
        </div>
      </div>
    );
  }

  return (
    <Card className={`${className}`}>
      <CardContent className="p-4">
        <div className="flex items-start gap-3">
          <MdLanguage className="text-muted-foreground mt-1" size={20} />
          <div className="flex-1">
            <h4 className="font-medium text-sm mb-2">Language Versions</h4>
            {currentTitle && (
              <p className="text-xs text-muted-foreground mb-3">
                Current: {currentTitle}
              </p>
            )}
            <div className="space-y-2">
              {languageVersions.map((version) => (
                <div key={version.locale} className="flex items-center justify-between">
                  <div className="flex items-center gap-2">
                    <Badge 
                      variant={version.locale === locale ? "default" : "outline"}
                      className="text-xs"
                    >
                      {localeNames[version.locale]}
                    </Badge>
                    {version.exists ? (
                      <div className="flex items-center gap-1 text-xs text-green-600">
                        <MdCheck size={12} />
                        <span>Available</span>
                      </div>
                    ) : (
                      <div className="flex items-center gap-1 text-xs text-red-600">
                        <MdClose size={12} />
                        <span>Not available</span>
                      </div>
                    )}
                  </div>
                  {version.locale !== locale && (
                    <Button
                      variant="ghost"
                      size="sm"
                      className="h-6 px-2 text-xs"
                      onClick={() => handleLanguageSwitch(version.locale)}
                      disabled={!version.exists}
                    >
                      {version.exists ? 'Switch' : 'Go to list'}
                    </Button>
                  )}
                </div>
              ))}
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
