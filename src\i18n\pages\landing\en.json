{"template": "shipany-template-one", "theme": "light", "header": {"brand": {"title": "ShipAny", "logo": {"src": "/logo.png", "alt": "ShipAny"}, "url": "/"}, "nav": {"items": [{"title": "Features", "url": "/#feature", "icon": "RiSparkling2Line"}, {"title": "Pricing", "url": "/pricing", "icon": "RiMoneyDollarCircleLine"}, {"title": "Showcase", "url": "/showcase", "icon": "RiApps2Line"}, {"title": "Blogs", "url": "/blogs", "icon": "RiArticleLine"}, {"title": "Products", "url": "/products", "icon": "RiProductHuntLine"}, {"title": "Case Studies", "url": "/case-studies", "icon": "RiFileTextLine"}]}, "buttons": [{"title": "Get ShipAny", "url": "https://shipany.ai", "target": "_blank", "variant": "link", "icon": "RiArrowRightUpLine"}], "show_sign": true, "show_theme": true, "show_locale": true}, "hero": {"title": "Ship Any AI Startups in hours, not days", "highlight_text": "Ship Any", "description": "ShipAny is a NextJS boilerplate for building AI SaaS startups.<br/>Ship Fast with a variety of templates and components.", "announcement": {"label": "2025", "title": "🎉 Happy New Year", "url": "/#pricing"}, "tip": "🎁 50% off before 2025", "buttons": [{"title": "Get Started", "icon": "RiFlashlightFill", "url": "/#pricing", "target": "_self", "variant": "default"}, {"title": "Join <PERSON>", "icon": "RiDiscordFill", "url": "https://discord.gg/HQNnrzjZQS", "target": "_blank", "variant": "outline"}], "show_happy_users": true, "show_badge": false}, "branding": {"title": "ShipAny is built on the shoulders of giants", "items": [{"title": "Next.js", "image": {"src": "/imgs/logos/nextjs.svg", "alt": "Next.js"}}, {"title": "React", "image": {"src": "/imgs/logos/react.svg", "alt": "React"}}, {"title": "TailwindCSS", "image": {"src": "/imgs/logos/tailwindcss.svg", "alt": "TailwindCSS"}}, {"title": "Shadcn/UI", "image": {"src": "/imgs/logos/shadcn.svg", "alt": "Shadcn/UI"}}, {"title": "Vercel", "image": {"src": "/imgs/logos/vercel.svg", "alt": "Vercel"}}]}, "introduce": {"name": "introduce", "title": "What is ShipAny", "label": "Introduce", "description": "ShipAny is a NextJS boilerplate for building AI SaaS startups. Built in a variety of templates and components.", "image": {"src": "/imgs/features/1.png"}, "items": [{"title": "Ready-to-use Templates", "description": "Choose from dozens of production-ready AI SaaS templates to quickly start your project.", "icon": "RiNextjsFill"}, {"title": "Infrastructure Setup", "description": "Get instant access to scalable infrastructure with best practices built-in.", "icon": "RiDatabase2Line"}, {"title": "Quick Deployment", "description": "Deploy your AI SaaS application to production in hours, not days.", "icon": "RiCloudyFill"}]}, "benefit": {"name": "benefit", "title": "Why Choose ShipAny", "label": "Benefits", "description": "Get everything you need to launch your AI startup - from ready-to-use templates to technical support.", "items": [{"title": "Complete Framework", "description": "Built on Next.js with authentication, payments, and AI integration - everything works out of the box.", "icon": "RiNextjsFill", "image": {"src": "/imgs/features/2.png"}}, {"title": "Rich Templates Library", "description": "Choose from various AI SaaS templates to kickstart your project - chatbots, image generation, and more.", "icon": "RiClapperboardAiLine", "image": {"src": "/imgs/features/3.png"}}, {"title": "Technical Guidance", "description": "Get dedicated support and join our developer community to ensure your successful launch.", "icon": "RiCodeFill", "image": {"src": "/imgs/features/4.png"}}]}, "usage": {"name": "usage", "title": "How to Launch with ShipAny", "description": "Get your AI SaaS startup running in three simple steps:", "image": {"src": "/imgs/features/1.png"}, "image_position": "left", "text_align": "center", "items": [{"title": "Get ShipAny", "description": "Buy ShipAny with a one-time payment. Check your email for the code and documentation.", "image": {"src": "/imgs/features/5.png"}}, {"title": "Start Your Project", "description": "Read the documentation and clone the code of ShipAny. Start building your AI SaaS startup.", "image": {"src": "/imgs/features/6.png"}}, {"title": "Customize Your Project", "description": "Modify the template with your data and contents. Specific AI functionality needs.", "image": {"src": "/imgs/features/7.png"}}, {"title": "Deploy to Production", "description": "Deploy your project to production with a few steps and start serving customers immediately.", "image": {"src": "/imgs/features/8.png"}}]}, "feature": {"name": "feature", "title": "Key Features of ShipAny", "description": "Everything you need to launch your AI SaaS startup quickly and efficiently.", "items": [{"title": "Next.js Boilerplate", "description": "Production-ready Next.js templates with SEO-friendly structure and i18n support.", "icon": "RiNextjsFill"}, {"title": "Authentication & Payments", "description": "Integrated Google OAuth, one-tap login, and Stripe payment processing.", "icon": "RiKey2Fill"}, {"title": "Data Infrastructure", "description": "Built-in Supabase integration for reliable and scalable data storage.", "icon": "RiDatabase2Line"}, {"title": "One-Click Deployment", "description": "Seamless deployment to Vercel or Cloudflare with automated setup.", "icon": "RiCloudy2Fill"}, {"title": "Business Analytics", "description": "Integrated Google Analytics and Search Console for tracking growth.", "icon": "RiBarChart2Line"}, {"title": "AI-Ready Infrastructure", "description": "Pre-configured AI integration with built-in credits system and API sales.", "icon": "RiRobot2Line"}]}, "stats": {"name": "stats", "label": "Stats", "title": "People Love ShipAny", "description": "for it's easy to use and fast to ship.", "icon": "FaRegHeart", "items": [{"title": "Trusted by", "label": "99+", "description": "Customers"}, {"title": "Built in", "label": "20+", "description": "Components"}, {"title": "Ship Fast in", "label": "5", "description": "Minutes"}]}, "testimonial": {"name": "testimonial", "label": "Testimonial", "title": "What Users Say About ShipAny", "description": "Hear from developers and founders who launched their AI startups with ShipAny.", "icon": "GoThumbsup", "items": [{"title": "<PERSON>", "label": "Founder of AIWallpaper.shop", "description": "ShipAny saved us months of development time. We launched our AI wallpaper business in just 2 days and got our first paying customer within a week!", "image": {"src": "/imgs/users/1.png"}}, {"title": "<PERSON>", "label": "CTO at HeyBeauty.ai", "description": "The pre-built AI infrastructure is a game-changer. We didn't have to worry about architecture - just focused on our AI beauty tech and went live fast.", "image": {"src": "/imgs/users/2.png"}}, {"title": "<PERSON>", "label": "Solo Developer", "description": "As a solo developer, <PERSON><PERSON><PERSON> gave me everything I needed - auth, payments, AI integration, and beautiful UI. Launched my SaaS in a weekend!", "image": {"src": "/imgs/users/3.png"}}, {"title": "<PERSON> Garcia", "label": "CEO of Melodisco", "description": "The templates are production-ready and highly customizable. We built our AI music platform in hours instead of months. Incredible time-to-market!", "image": {"src": "/imgs/users/4.png"}}, {"title": "<PERSON>", "label": "Tech Lead at GPTs.works", "description": "ShipAny's infrastructure is rock-solid. We scaled from 0 to 10k users without touching the backend. Best investment for our AI startup.", "image": {"src": "/imgs/users/5.png"}}, {"title": "<PERSON>", "label": "Startup Founder", "description": "From idea to launch in 3 days! ShipAny's templates and deployment tools made it possible to test our AI business concept incredibly fast.", "image": {"src": "/imgs/users/6.png"}}]}, "faq": {"name": "faq", "label": "FAQ", "title": "Frequently Asked Questions About ShipAny", "description": "Have another question? Contact us on Discord or by email.", "items": [{"title": "What exactly is <PERSON><PERSON><PERSON> and how does it work?", "description": "ShipAny is a comprehensive NextJS boilerplate designed specifically for building AI SaaS startups. It provides ready-to-use templates, infrastructure setup, and deployment tools that help you launch your AI business in hours instead of days."}, {"title": "Do I need advanced technical skills to use ShipAny?", "description": "While basic programming knowledge is helpful, ShipAny is designed to be developer-friendly. Our templates and documentation make it easy to get started, even if you're not an expert in AI or cloud infrastructure."}, {"title": "What types of AI SaaS can I build with ShipAny?", "description": "ShipAny supports a wide range of AI applications, from content generation to data analysis tools. Our templates cover popular use cases like AI chatbots, content generators, image processing apps, and more."}, {"title": "How long does it typically take to launch with ShipAny?", "description": "With ShipAny, you can have a working prototype in hours and a production-ready application in hours. Our one-click deployment and pre-configured infrastructure significantly reduce the traditional months-long development cycle."}, {"title": "What's included in the ShipAny infrastructure?", "description": "ShipAny provides a complete infrastructure stack including authentication, database setup, API integration, payment processing, and scalable cloud deployment. Everything is pre-configured following industry best practices."}, {"title": "Can I customize the templates to match my brand?", "description": "Absolutely! All ShipAny templates are fully customizable. You can modify the design, features, and functionality to match your brand identity and specific business requirements while maintaining the robust underlying infrastructure."}]}, "cta": {"name": "cta", "title": "Ship your first AI SaaS Startup", "description": "Start from here, ship with ShipAny.", "buttons": [{"title": "Get ShipAny", "url": "https://shipany.ai", "target": "_blank", "icon": "GoArrowUpRight"}, {"title": "Read Document", "url": "https://docs.shipany.ai", "target": "_blank", "variant": "outline"}]}, "footer": {"name": "footer", "brand": {"title": "ShipAny", "description": "ShipAny is a NextJS boilerplate for building AI SaaS startups. Ship Fast with a variety of templates and components.", "logo": {"src": "/logo.png", "alt": "ShipAny"}, "url": "/"}, "copyright": "© 2025 • ShipAny All rights reserved.", "nav": {"items": [{"title": "About", "children": [{"title": "Features", "url": "/#feature", "target": "_self"}, {"title": "Showcases", "url": "/#showcase", "target": "_self"}, {"title": "Pricing", "url": "/#pricing", "target": "_self"}]}, {"title": "Resources", "children": [{"title": "Documents", "url": "https://docs.shipany.ai", "target": "_blank"}, {"title": "Components", "url": "https://shipany.ai/components", "target": "_blank"}, {"title": "Templates", "url": "https://shipany.ai/templates", "target": "_blank"}]}, {"title": "Friends", "children": [{"title": "ThinkAny", "url": "https://thinkany.ai", "target": "_blank"}, {"title": "HeyBeauty", "url": "https://heybeauty.ai", "target": "_blank"}, {"title": "<PERSON><PERSON>", "url": "https://pagen.so", "target": "_blank"}]}]}, "social": {"items": [{"title": "X", "icon": "RiTwitterXFill", "url": "https://x.com/shipanyai", "target": "_blank"}, {"title": "<PERSON><PERSON><PERSON>", "icon": "RiGithubFill", "url": "https://github.com/shipanyai", "target": "_blank"}, {"title": "Discord", "icon": "RiDiscordFill", "url": "https://discord.gg/HQNnrzjZQS", "target": "_blank"}, {"title": "Email", "icon": "RiMailLine", "url": "mailto:<EMAIL>", "target": "_self"}]}, "agreement": {"items": [{"title": "Privacy Policy", "url": "/privacy-policy"}, {"title": "Terms of Service", "url": "/terms-of-service"}]}}}