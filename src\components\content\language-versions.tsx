"use client";

import { Badge } from "@/components/ui/badge";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { Separator } from "@/components/ui/separator";
import { 
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";
import { useParams, usePathname, useRouter } from "next/navigation";
import { toast } from "sonner";
import { Globe, Check, ExternalLink } from "lucide-react";

import { localeNames, locales } from "@/i18n/locale";
import { 
  detectContentPage,
  getAvailableLanguageVersions,
  handleContentLanguageSwitch,
  getContentTitle,
  type ContentType
} from "@/lib/content-language-utils";

interface LanguageVersionsProps {
  /**
   * Whether to show as a compact inline version
   */
  compact?: boolean;
  /**
   * Custom class name for styling
   */
  className?: string;
}

/**
 * Language Versions Indicator Component
 * 
 * Displays available language versions for the current content and provides
 * smart language switching functionality. Shows which languages are available
 * and allows users to switch between them.
 */
export default function LanguageVersions({ 
  compact = false, 
  className = "" 
}: LanguageVersionsProps) {
  const params = useParams();
  const locale = params.locale as string;
  const router = useRouter();
  const pathname = usePathname();

  // Detect current content page information
  const contentInfo = detectContentPage(pathname, locale);
  
  // Only show for content pages with a slug
  if (contentInfo.type === 'other' || !contentInfo.slug) {
    return null;
  }

  // Get available language versions
  const languageVersions = getAvailableLanguageVersions(
    contentInfo.type,
    contentInfo.slug,
    locales
  );

  // Check if there are multiple language versions available
  const availableVersions = languageVersions.filter(version => version.exists);
  
  // Don't show if only one language version exists
  if (availableVersions.length <= 1) {
    return null;
  }

  const handleLanguageSwitch = (targetLocale: string) => {
    if (targetLocale === locale) return;

    const switchResult = handleContentLanguageSwitch(
      pathname,
      locale,
      targetLocale,
      locales
    );

    router.push(switchResult.url);

    // Show feedback for fallback scenarios
    if (switchResult.strategy === 'fallback-list') {
      toast.info(
        `This content is not available in ${localeNames[targetLocale]}. Redirected to the content list.`,
        { duration: 4000 }
      );
    }
  };

  const getContentTypeLabel = (type: ContentType): string => {
    switch (type) {
      case 'blog':
        return 'Article';
      case 'product':
        return 'Product';
      case 'case-study':
        return 'Case Study';
      default:
        return 'Content';
    }
  };

  if (compact) {
    return (
      <div className={`flex items-center gap-2 text-sm text-muted-foreground ${className}`}>
        <Globe className="h-4 w-4" />
        <span>Available in:</span>
        <div className="flex gap-1">
          {languageVersions.map((version) => (
            <TooltipProvider key={version.locale}>
              <Tooltip>
                <TooltipTrigger asChild>
                  {version.exists ? (
                    <Button
                      variant={version.locale === locale ? "default" : "outline"}
                      size="sm"
                      className="h-6 px-2 text-xs"
                      onClick={() => handleLanguageSwitch(version.locale)}
                      disabled={version.locale === locale}
                    >
                      {version.locale === locale && <Check className="h-3 w-3 mr-1" />}
                      {localeNames[version.locale]}
                    </Button>
                  ) : (
                    <Badge variant="secondary" className="h-6 px-2 text-xs opacity-50">
                      {localeNames[version.locale]}
                    </Badge>
                  )}
                </TooltipTrigger>
                <TooltipContent>
                  {version.exists 
                    ? version.locale === locale 
                      ? "Current language"
                      : `Switch to ${localeNames[version.locale]}`
                    : `Not available in ${localeNames[version.locale]}`
                  }
                </TooltipContent>
              </Tooltip>
            </TooltipProvider>
          ))}
        </div>
      </div>
    );
  }

  return (
    <Card className={`mb-6 ${className}`}>
      <CardContent className="p-4">
        <div className="flex items-start justify-between">
          <div className="flex items-center gap-2 mb-3">
            <Globe className="h-5 w-5 text-muted-foreground" />
            <h3 className="font-medium">Language Versions</h3>
          </div>
          <Badge variant="outline" className="text-xs">
            {getContentTypeLabel(contentInfo.type)}
          </Badge>
        </div>
        
        <p className="text-sm text-muted-foreground mb-4">
          This {getContentTypeLabel(contentInfo.type).toLowerCase()} is available in the following languages:
        </p>

        <div className="space-y-2">
          {languageVersions.map((version, index) => {
            const isCurrentLanguage = version.locale === locale;
            const currentTitle = getContentTitle(contentInfo.type, contentInfo.slug!, version.locale);
            
            return (
              <div key={version.locale}>
                <div className="flex items-center justify-between p-3 rounded-lg border bg-muted/30">
                  <div className="flex items-center gap-3">
                    <div className="flex items-center gap-2">
                      {isCurrentLanguage && <Check className="h-4 w-4 text-green-600" />}
                      <span className="font-medium">
                        {localeNames[version.locale]}
                      </span>
                      {isCurrentLanguage && (
                        <Badge variant="default" className="text-xs">
                          Current
                        </Badge>
                      )}
                    </div>
                    {currentTitle && (
                      <span className="text-sm text-muted-foreground truncate max-w-xs">
                        "{currentTitle}"
                      </span>
                    )}
                  </div>
                  
                  <div className="flex items-center gap-2">
                    {version.exists ? (
                      !isCurrentLanguage && (
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => handleLanguageSwitch(version.locale)}
                          className="flex items-center gap-1"
                        >
                          <ExternalLink className="h-3 w-3" />
                          Switch
                        </Button>
                      )
                    ) : (
                      <Badge variant="secondary" className="text-xs">
                        Not Available
                      </Badge>
                    )}
                  </div>
                </div>
                {index < languageVersions.length - 1 && (
                  <Separator className="my-2" />
                )}
              </div>
            );
          })}
        </div>

        <div className="mt-4 pt-3 border-t">
          <p className="text-xs text-muted-foreground">
            💡 You can also use the language selector in the header to switch between languages.
          </p>
        </div>
      </CardContent>
    </Card>
  );
}
