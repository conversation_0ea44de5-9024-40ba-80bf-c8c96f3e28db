// NOTE This file is auto-generated by Contentlayer

import type { Markdown, MDX, ImageFieldData, IsoDateTimeString } from 'contentlayer/core'
import * as Local from 'contentlayer/source-files'

export { isType } from 'contentlayer/client'

export type { Markdown, MDX, ImageFieldData, IsoDateTimeString }

/** Document types */
export type Blog = {
  /** File path relative to `contentDirPath` */
  _id: string
  _raw: Local.RawDocumentData
  type: 'Blog'
  title: string
  slug: string
  description?: string | undefined
  coverImage?: string | undefined
  author?: string | undefined
  authorImage?: string | undefined
  publishedAt?: IsoDateTimeString | undefined
  featured: boolean
  tags?: string[] | undefined
  videoUrl?: string | undefined
  videoThumbnail?: string | undefined
  videoDuration?: string | undefined
  /** MDX file body */
  body: MDX
  lang: string
  url: string
  createdAt: date
}

export type CaseStudy = {
  /** File path relative to `contentDirPath` */
  _id: string
  _raw: Local.RawDocumentData
  type: 'CaseStudy'
  title: string
  slug: string
  description?: string | undefined
  coverImage?: string | undefined
  author?: string | undefined
  authorImage?: string | undefined
  publishedAt?: IsoDateTimeString | undefined
  featured: boolean
  tags?: string[] | undefined
  videoUrl?: string | undefined
  videoThumbnail?: string | undefined
  videoDuration?: string | undefined
  /** MDX file body */
  body: MDX
  lang: string
  url: string
  createdAt: date
}

export type Product = {
  /** File path relative to `contentDirPath` */
  _id: string
  _raw: Local.RawDocumentData
  type: 'Product'
  title: string
  slug: string
  description?: string | undefined
  coverImage?: string | undefined
  author?: string | undefined
  authorImage?: string | undefined
  publishedAt?: IsoDateTimeString | undefined
  featured: boolean
  tags?: string[] | undefined
  videoUrl?: string | undefined
  videoThumbnail?: string | undefined
  videoDuration?: string | undefined
  /** MDX file body */
  body: MDX
  lang: string
  url: string
  createdAt: date
}  

/** Nested types */
  

/** Helper types */

export type AllTypes = DocumentTypes | NestedTypes
export type AllTypeNames = DocumentTypeNames | NestedTypeNames

export type DocumentTypes = Blog | CaseStudy | Product
export type DocumentTypeNames = 'Blog' | 'CaseStudy' | 'Product'

export type NestedTypes = never
export type NestedTypeNames = never

export type DataExports = {
  allDocuments: DocumentTypes[]
  allBlogs: Blog[]
  allProducts: Product[]
  allCaseStudies: CaseStudy[]
}


export interface ContentlayerGenTypes {
  documentTypes: DocumentTypes
  documentTypeMap: DocumentTypeMap
  documentTypeNames: DocumentTypeNames
  nestedTypes: NestedTypes
  nestedTypeMap: NestedTypeMap
  nestedTypeNames: NestedTypeNames
  allTypeNames: AllTypeNames
  dataExports: DataExports
}

declare global {
  interface ContentlayerGen extends ContentlayerGenTypes {}
}

export type DocumentTypeMap = {
  Blog: Blog
  CaseStudy: CaseStudy
  Product: Product
}

export type NestedTypeMap = {

}

 