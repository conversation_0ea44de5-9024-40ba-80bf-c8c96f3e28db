/**
 * Contentlayer Configuration
 *
 * This file configures Contentlayer to process MDX files for our multi-language
 * content management system. It defines document types for blogs, products, and
 * case studies with consistent field structures and computed properties.
 */

import { defineDocumentType, makeSource } from 'contentlayer/source-files'
import fs from 'fs'
import path from 'path'

/**
 * Factory function to create common document type configuration
 *
 * @param name - The document type name (e.g., 'Blog', 'Product')
 * @param pattern - The directory pattern to match files (e.g., 'blogs', 'products')
 * @returns Document type configuration object
 */
const makeCommon = (name: string, pattern: string) => ({
  name,
  // Match all MDX files in the specified pattern directory and subdirectories
  filePathPattern: `${pattern}/**/*.mdx`,
  contentType: 'mdx' as const,

  // Define the frontmatter fields that each MDX file should contain
  fields: {
    title: { type: 'string' as const, required: true },           // Article/product title
    slug: { type: 'string' as const, required: true },            // URL slug for routing
    description: { type: 'string' as const, required: false },    // Meta description for SEO
    coverImage: { type: 'string' as const, required: false },     // Hero/cover image URL
    author: { type: 'string' as const, required: false },         // Author name
    authorImage: { type: 'string' as const, required: false },    // Author avatar URL
    publishedAt: { type: 'date' as const, required: false },      // Publication date
    featured: { type: 'boolean' as const, required: false, default: false }, // Featured flag
    tags: { type: 'list' as const, of: { type: 'string' as const }, required: false }, // Content tags
    // Video-related fields
    videoUrl: { type: 'string' as const, required: false },       // Self-hosted video URL
    videoThumbnail: { type: 'string' as const, required: false }, // Video thumbnail/poster image
    videoDuration: { type: 'string' as const, required: false },  // Video duration (e.g., "5:30")
  },

  // Computed fields are automatically generated based on file path and metadata
  computedFields: {
    // Extract language from file path (e.g., 'blogs/en/post.mdx' -> 'en')
    lang: {
      type: 'string' as const,
      resolve: (doc: any) => doc._raw.flattenedPath.split('/')[1]
    },

    // Generate the full URL path for the content
    url: {
      type: 'string' as const,
      resolve: (doc: any) => `/${doc._raw.flattenedPath.split('/')[0]}/${doc._raw.flattenedPath.split('/')[1]}/${doc.slug}`
    },

    // Get file creation time from filesystem
    createdAt: {
      type: 'date' as const,
      resolve: (doc: any) => fs.statSync(path.join('content', doc._raw.sourceFilePath)).birthtime
    }
  }
})

// Define document types for each content category
export const Blog = defineDocumentType(() => makeCommon('Blog', 'blogs'))
export const Product = defineDocumentType(() => makeCommon('Product', 'products'))
export const CaseStudy = defineDocumentType(() => makeCommon('CaseStudy', 'case-studies'))

/**
 * Main Contentlayer configuration
 *
 * This configuration tells Contentlayer where to find content files and
 * which document types to process. All MDX files in the 'content' directory
 * will be processed according to their respective document type definitions.
 */
export default makeSource({
  contentDirPath: 'content',                           // Root directory for content files
  documentTypes: [Blog, Product, CaseStudy],          // Document types to process
})
