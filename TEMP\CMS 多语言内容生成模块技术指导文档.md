# 🚀 CMS 多语言内容生成模块技术指导文档

## 1. 方案概述

本方案提供两种主流技术路线：

+ **本地 MDX 内容管理（Contentlayer + Next.js）**：推荐优先落地，支持极高灵活性、开发体验好、便于版本管理。
+ **Headless CMS 方案（Strapi / Sanity 等）**：适合需要团队可视化后台、内容协作或后期扩展更强内容模型时快速切换。

> 主要目标：管理博客、产品页、案例页等内容，支持多语言（如 `en`/`zh`），并自动生成 sitemap、RSS feed，实现 SEO 友好和类型安全内链。
>

---

## 2. 目录结构（建议实践标准）

```plain
my-site/
├── app/
│   └── [lang]/
│       ├── blog/
│       │   ├── page.tsx
│       │   └── [slug]/page.tsx
│       ├── product/
│       │   ├── page.tsx
│       │   └── [slug]/page.tsx
│       └── casestudy/
│           ├── page.tsx
│           └── [slug]/page.tsx
│       └── layout.tsx      # 支持多语言切换、导航
├── content/
│   ├── blog/
│   │   ├── en/*.mdx
│   │   └── zh/*.mdx
│   ├── product/
│   │   ├── en/*.mdx
│   │   └── zh/*.mdx
│   └── casestudy/
│       ├── en/*.mdx
│       └── zh/*.mdx
├── components/
│   └── Mdx.tsx             # MDX 渲染组件
├── contentlayer.config.ts  # 内容模型配置
├── next.config.js          # Next.js + Contentlayer + i18n
├── lib/
│   └── $path.ts            # Pathpida 输出（类型安全内链）
├── scripts/
│   ├── sitemap.ts
│   └── rss.js
├── public/
│   └── rss.xml
└── package.json
```

---

## 3. 本地 MDX 方案实现

### a) Contentlayer 内容模型配置

```typescript
// contentlayer.config.ts
import { defineDocumentType, makeSource } from 'contentlayer/source-files'
import fs from 'fs'
import path from 'path'

const makeCommon = (name: string, pattern: string) => ({
  name,
  filePathPattern: `${pattern}/**/*.mdx`,
  contentType: 'mdx',
  fields: {
    title: { type: 'string', required: true },
    slug: { type: 'string', required: true }
  },
  computedFields: {
    lang: { type: 'string', resolve: doc => doc._raw.flattenedPath.split('/')[1] },
    url: { type: 'string', resolve: doc => `/${doc._raw.flattenedPath}` },
    createdAt: {
      type: 'date',
      resolve: doc => fs.statSync(path.join('content', doc._raw.sourceFilePath)).birthtime
    }
  }
})

export const Blog = defineDocumentType(() => makeCommon('Blog', 'blog'))
export const Product = defineDocumentType(() => makeCommon('Product', 'product'))
export const CaseStudy = defineDocumentType(() => makeCommon('CaseStudy', 'casestudy'))

export default makeSource({
  contentDirPath: 'content',
  documentTypes: [Blog, Product, CaseStudy],
})
```

### b) Next.js 国际化与内容注入

```javascript
// next.config.js
const { withContentlayer } = require('next-contentlayer')
module.exports = withContentlayer({
  i18n: { locales: ['en','zh'], defaultLocale: 'zh' },
  pageExtensions: ['js','jsx','ts','tsx','md','mdx']
})
```

### c) MDX 渲染组件

```tsx
// components/Mdx.tsx
'use client'
import Image from 'next/image'
import { useMDXComponent } from 'next-contentlayer/hooks'

export function Mdx({ code }: { code: string }) {
  const Component = useMDXComponent(code)
  return <Component components={{ img: props => <Image {...props} alt={props.alt}/> }} />
}
```

### d) 动态页面实现（以博客详情页为例）

```tsx
// app/[lang]/blog/[slug]/page.tsx
import { allBlogs } from 'contentlayer/generated'
import { notFound } from 'next/navigation'
import { Mdx } from '@/components/Mdx'

export async function generateStaticParams() {
  return allBlogs.map(p => ({ lang: p.lang, slug: p.slug }))
}

export default function BlogPage({ params: { lang, slug } }) {
  const post = allBlogs.find(p => p.lang === lang && p.slug === slug)
  if (!post) return notFound()
  return (
    <article>
      <h1>{post.title}</h1>
      <time dateTime={post.createdAt.toISOString()}>
        {post.createdAt.toLocaleDateString()}
      </time>
      <Mdx code={post.body.code}/>
    </article>
  )
}
```

产品页/案例页实现方式一致，只需切换内容源。

### e) 列表页与类型安全内链

```tsx
import Link from 'next/link'
import { pagesPath } from '@/lib/$path'
import { allBlogs } from 'contentlayer/generated'

export default function BlogIndex({ params: { lang } }) {
  return (
    <ul>
      {allBlogs.filter(p => p.lang === lang).map(p => (
        <li key={p.slug}>
          <Link href={pagesPath[lang].blog._slug(p.slug).$url()}>{p.title}</Link>
        </li>
      ))}
    </ul>
  )
}
```

Pathpida 自动类型校验，避免拼写错误。

---

## 4. SEO 自动化（sitemap & RSS）

### a) sitemap 生成

```typescript
// scripts/sitemap.ts
import { allBlogs, allProduct, allCaseStudy } from 'contentlayer/generated'
import { MetadataRoute } from 'next'

export default function sitemap(): MetadataRoute.Sitemap {
  return [...allBlogs, ...allProduct, ...allCaseStudy].map(doc => ({
    url: `https://example.com${doc.url}`,
    lastModified: doc.createdAt
  }))
}
```

### b) RSS 生成

```javascript
// scripts/rss.js
import RSS from 'rss'
import fs from 'fs'
import { allBlogs } from '.contentlayer/generated'

const feed = new RSS({ title: 'My Site', feed_url: 'https://example.com/rss.xml', site_url: 'https://example.com' })
allBlogs.forEach(p => feed.item({ title: p.title, url: `https://example.com${p.url}`, date: p.createdAt }))
fs.writeFileSync('public/rss.xml', feed.xml({ indent: true }))
```

在 `package.json` 里写：

```json
"build": "next build && node scripts/rss.js"
```

---

## 5. Headless CMS 方案简要（Strapi/Sanity）

### a) 选型建议

+ **Strapi**：开源、自托管、强多语言、REST/GraphQL
+ **Sanity**：云托管、协作强、GROQ 查询
+ **其他**：Payload、Storyblok、Contentful等均支持多语言

### b) 建模与多语言管理

1. CMS 后台建立 Blog、Product、CaseStudy 三模型
2. 启用多语言/locale/i18n 插件
3. 每条内容为每种语言建独立 entry，内容可自动关联
4. CMS 会自动生成多语言 API 接口：

```plain
GET /api/blogs?locale=zh
GET /api/products?slug=awesome-widget&locale=en
```

### c) Next.js 集成 Headless CMS API

```typescript
// app/[lang]/product/[slug]/page.tsx
export async function generateStaticParams() {
  const res = await fetch(`${CMS_URL}/products?locale=*`);
  const items = await res.json();
  return items.map(p => ({ lang: p.locale, slug: p.slug }));
}

export default async function Page({ params }) {
  const { lang, slug } = params;
  const res = await fetch(`${CMS_URL}/products?slug=${slug}&locale=${lang}&populate=*`);
  const product = (await res.json())[0];
  if (!product) notFound();
  return (
    <article>
      <h1>{product.title}</h1>
      <div dangerouslySetInnerHTML={{ __html: product.content }} />
    </article>
  );
}
```

### d) SEO 自动化同上

+ sitemap.ts/RSS.js 只需换为远程 fetch API 拉取内容构建即可。
+ 路径风格、类型安全链接等可以继续使用 Pathpida。

---

## 6. 关键知识点梳理 & 实践路线

| 阶段 | 内容管理 | 路由渲染 | 类型安全链接 | SEO 自动化 | 后备扩展 |
| --- | --- | --- | --- | --- | --- |
| 方案一 | 本地 MDX + Contentlayer | Next.js App Router | Pathpida | sitemap + RSS 脚本 | Headless CMS 可切换 |
| 方案二 | Strapi/Sanity/Storyblok | Next.js API fetch | Pathpida | sitemap + RSS 脚本 | 便于协作、后台 |

---

## 7. 快速起步指引

1. **Clone 本文项目结构，安装依赖（见 package.json）**
2. **编写多语言内容（MDX 或 CMS 后台录入）**
3. **集成内容源（本地或 API）并配置渲染组件**
4. **运行 **`npm run dev`** 预览，build 时自动生成 sitemap/rss**
5. **后期如需引入可视化后台/团队协作，无缝切换 Headless CMS**

---

## 8. 参考/扩展

+ Contentlayer 官方文档：[https://contentlayer.dev/](https://contentlayer.dev/)
+ Strapi 官方文档与 Next.js 集成
+ Sanity、Storyblok、Contentful 相关 Next.js 多语言实践
+ Pathpida 用于类型安全路由：[Pathpida GitHub](https://github.com/aspida/pathpida)
