/**
 * Route Utilities
 *
 * This module provides utilities for generating type-safe routes and URLs
 * for our multi-language application. It includes path generation functions,
 * canonical URL builders, and internationalization helpers that ensure
 * consistent routing across different locales and content types.
 */

import { locales } from '@/i18n/locale'

type Locale = typeof locales[number]

interface PathParams {
  locale?: Locale  // Language locale (e.g., 'en', 'zh')
  slug?: string    // Content slug for dynamic routes
}

/**
 * Path generation functions for different content types and pages
 *
 * Each function handles locale-specific URL generation. English (en) is treated
 * as the default locale and doesn't include a locale prefix in the URL.
 */
export const paths = {
  // Home page path
  home: (locale?: Locale) => locale && locale !== 'en' ? `/${locale}` : '/',

  // Blog-related paths (MDX content)
  blogs: {
    index: (locale?: Locale) => locale && locale !== 'en' ? `/${locale}/blogs` : '/blogs',
    post: (slug: string, locale?: Locale) =>
      locale && locale !== 'en' ? `/${locale}/blogs/${slug}` : `/blogs/${slug}`,
  },

  // Product-related paths (MDX content)
  products: {
    index: (locale?: Locale) => locale && locale !== 'en' ? `/${locale}/products` : '/products',
    detail: (slug: string, locale?: Locale) =>
      locale && locale !== 'en' ? `/${locale}/products/${slug}` : `/products/${slug}`,
  },

  // Case study paths (MDX content)
  caseStudies: {
    index: (locale?: Locale) => locale && locale !== 'en' ? `/${locale}/case-studies` : '/case-studies',
    detail: (slug: string, locale?: Locale) =>
      locale && locale !== 'en' ? `/${locale}/case-studies/${slug}` : `/case-studies/${slug}`,
  },

  // Posts paths (database-driven content, existing system)
  posts: {
    index: (locale?: Locale) => locale && locale !== 'en' ? `/${locale}/posts` : '/posts',
    detail: (slug: string, locale?: Locale) =>
      locale && locale !== 'en' ? `/${locale}/posts/${slug}` : `/posts/${slug}`,
  },

  // Static page paths
  pricing: (locale?: Locale) => locale && locale !== 'en' ? `/${locale}/pricing` : '/pricing',
  showcase: (locale?: Locale) => locale && locale !== 'en' ? `/${locale}/showcase` : '/showcase',
} as const

/**
 * Type-safe path builder utility
 *
 * This function provides a type-safe way to build paths for top-level routes.
 * For nested routes (like blogs.post), use the specific path functions directly.
 *
 * @param pathKey - The key from the paths object
 * @param params - Optional parameters including locale
 * @returns Generated path string
 */
export function buildPath<T extends keyof typeof paths>(
  pathKey: T,
  params?: PathParams
): string {
  const pathBuilder = paths[pathKey]

  if (typeof pathBuilder === 'function') {
    return pathBuilder(params?.locale)
  }

  // For nested objects like blogs.post, products.detail, etc.
  return ''
}

/**
 * Generate canonical URL for SEO purposes
 *
 * Creates a full URL with the correct locale prefix for SEO meta tags.
 * English content doesn't include a locale prefix.
 *
 * @param path - The relative path
 * @param locale - Optional locale override
 * @returns Full canonical URL
 */
export function getCanonicalUrl(path: string, locale?: Locale): string {
  const baseUrl = process.env.NEXT_PUBLIC_WEB_URL || 'https://shipany.ai'

  if (locale && locale !== 'en' && !path.startsWith(`/${locale}`)) {
    return `${baseUrl}/${locale}${path}`
  }

  return `${baseUrl}${path}`
}

/**
 * Generate alternate language URLs for hreflang meta tags
 *
 * Creates a mapping of all supported locales to their respective URLs
 * for the same content. This is used for SEO hreflang attributes.
 *
 * @param path - The current path
 * @returns Object mapping each locale to its URL
 */
export function getAlternateUrls(path: string): Record<Locale, string> {
  const baseUrl = process.env.NEXT_PUBLIC_WEB_URL || 'https://shipany.ai'
  const alternates: Record<string, string> = {}

  locales.forEach(locale => {
    if (locale === 'en') {
      // Remove locale prefix for English (default locale)
      const cleanPath = path.replace(/^\/[a-z]{2}/, '') || '/'
      alternates[locale] = `${baseUrl}${cleanPath}`
    } else {
      // Add locale prefix for non-English languages
      const cleanPath = path.replace(/^\/[a-z]{2}/, '') || '/'
      alternates[locale] = `${baseUrl}/${locale}${cleanPath}`
    }
  })

  return alternates as Record<Locale, string>
}
