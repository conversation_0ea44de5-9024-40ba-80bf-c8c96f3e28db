{"version": 3, "sources": ["../../../contentlayer.config.ts"], "sourcesContent": ["/**\n * Contentlayer Configuration\n *\n * This file configures Contentlayer to process MDX files for our multi-language\n * content management system. It defines document types for blogs, products, and\n * case studies with consistent field structures and computed properties.\n */\n\nimport { defineDocumentType, makeSource } from 'contentlayer/source-files'\nimport fs from 'fs'\nimport path from 'path'\n\n/**\n * Factory function to create common document type configuration\n *\n * @param name - The document type name (e.g., 'Blog', 'Product')\n * @param pattern - The directory pattern to match files (e.g., 'blogs', 'products')\n * @returns Document type configuration object\n */\nconst makeCommon = (name: string, pattern: string) => ({\n  name,\n  // Match all MDX files in the specified pattern directory and subdirectories\n  filePathPattern: `${pattern}/**/*.mdx`,\n  contentType: 'mdx' as const,\n\n  // Define the frontmatter fields that each MDX file should contain\n  fields: {\n    title: { type: 'string' as const, required: true },           // Article/product title\n    slug: { type: 'string' as const, required: true },            // URL slug for routing\n    description: { type: 'string' as const, required: false },    // Meta description for SEO\n    coverImage: { type: 'string' as const, required: false },     // Hero/cover image URL\n    author: { type: 'string' as const, required: false },         // Author name\n    authorImage: { type: 'string' as const, required: false },    // Author avatar URL\n    publishedAt: { type: 'date' as const, required: false },      // Publication date\n    featured: { type: 'boolean' as const, required: false, default: false }, // Featured flag\n    tags: { type: 'list' as const, of: { type: 'string' as const }, required: false }, // Content tags\n    // Video-related fields\n    videoUrl: { type: 'string' as const, required: false },       // Self-hosted video URL\n    videoThumbnail: { type: 'string' as const, required: false }, // Video thumbnail/poster image\n    videoDuration: { type: 'string' as const, required: false },  // Video duration (e.g., \"5:30\")\n  },\n\n  // Computed fields are automatically generated based on file path and metadata\n  computedFields: {\n    // Extract language from file path (e.g., 'blogs/en/post.mdx' -> 'en')\n    lang: {\n      type: 'string' as const,\n      resolve: (doc: any) => doc._raw.flattenedPath.split('/')[1]\n    },\n\n    // Generate the full URL path for the content\n    url: {\n      type: 'string' as const,\n      resolve: (doc: any) => `/${doc._raw.flattenedPath.split('/')[0]}/${doc._raw.flattenedPath.split('/')[1]}/${doc.slug}`\n    },\n\n    // Get file creation time from filesystem\n    createdAt: {\n      type: 'date' as const,\n      resolve: (doc: any) => fs.statSync(path.join('content', doc._raw.sourceFilePath)).birthtime\n    }\n  }\n})\n\n// Define document types for each content category\nexport const Blog = defineDocumentType(() => makeCommon('Blog', 'blogs'))\nexport const Product = defineDocumentType(() => makeCommon('Product', 'products'))\nexport const CaseStudy = defineDocumentType(() => makeCommon('CaseStudy', 'case-studies'))\n\n/**\n * Main Contentlayer configuration\n *\n * This configuration tells Contentlayer where to find content files and\n * which document types to process. All MDX files in the 'content' directory\n * will be processed according to their respective document type definitions.\n */\nexport default makeSource({\n  contentDirPath: 'content',                           // Root directory for content files\n  documentTypes: [Blog, Product, CaseStudy],          // Document types to process\n})\n"], "mappings": ";AAQA,SAAS,oBAAoB,kBAAkB;AAC/C,OAAO,QAAQ;AACf,OAAO,UAAU;AASjB,IAAM,aAAa,CAAC,MAAc,aAAqB;AAAA,EACrD;AAAA;AAAA,EAEA,iBAAiB,GAAG,OAAO;AAAA,EAC3B,aAAa;AAAA;AAAA,EAGb,QAAQ;AAAA,IACN,OAAO,EAAE,MAAM,UAAmB,UAAU,KAAK;AAAA;AAAA,IACjD,MAAM,EAAE,MAAM,UAAmB,UAAU,KAAK;AAAA;AAAA,IAChD,aAAa,EAAE,MAAM,UAAmB,UAAU,MAAM;AAAA;AAAA,IACxD,YAAY,EAAE,MAAM,UAAmB,UAAU,MAAM;AAAA;AAAA,IACvD,QAAQ,EAAE,MAAM,UAAmB,UAAU,MAAM;AAAA;AAAA,IACnD,aAAa,EAAE,MAAM,UAAmB,UAAU,MAAM;AAAA;AAAA,IACxD,aAAa,EAAE,MAAM,QAAiB,UAAU,MAAM;AAAA;AAAA,IACtD,UAAU,EAAE,MAAM,WAAoB,UAAU,OAAO,SAAS,MAAM;AAAA;AAAA,IACtE,MAAM,EAAE,MAAM,QAAiB,IAAI,EAAE,MAAM,SAAkB,GAAG,UAAU,MAAM;AAAA;AAAA;AAAA,IAEhF,UAAU,EAAE,MAAM,UAAmB,UAAU,MAAM;AAAA;AAAA,IACrD,gBAAgB,EAAE,MAAM,UAAmB,UAAU,MAAM;AAAA;AAAA,IAC3D,eAAe,EAAE,MAAM,UAAmB,UAAU,MAAM;AAAA;AAAA,EAC5D;AAAA;AAAA,EAGA,gBAAgB;AAAA;AAAA,IAEd,MAAM;AAAA,MACJ,MAAM;AAAA,MACN,SAAS,CAAC,QAAa,IAAI,KAAK,cAAc,MAAM,GAAG,EAAE,CAAC;AAAA,IAC5D;AAAA;AAAA,IAGA,KAAK;AAAA,MACH,MAAM;AAAA,MACN,SAAS,CAAC,QAAa,IAAI,IAAI,KAAK,cAAc,MAAM,GAAG,EAAE,CAAC,CAAC,IAAI,IAAI,KAAK,cAAc,MAAM,GAAG,EAAE,CAAC,CAAC,IAAI,IAAI,IAAI;AAAA,IACrH;AAAA;AAAA,IAGA,WAAW;AAAA,MACT,MAAM;AAAA,MACN,SAAS,CAAC,QAAa,GAAG,SAAS,KAAK,KAAK,WAAW,IAAI,KAAK,cAAc,CAAC,EAAE;AAAA,IACpF;AAAA,EACF;AACF;AAGO,IAAM,OAAO,mBAAmB,MAAM,WAAW,QAAQ,OAAO,CAAC;AACjE,IAAM,UAAU,mBAAmB,MAAM,WAAW,WAAW,UAAU,CAAC;AAC1E,IAAM,YAAY,mBAAmB,MAAM,WAAW,aAAa,cAAc,CAAC;AASzF,IAAO,8BAAQ,WAAW;AAAA,EACxB,gBAAgB;AAAA;AAAA,EAChB,eAAe,CAAC,MAAM,SAAS,SAAS;AAAA;AAC1C,CAAC;", "names": []}