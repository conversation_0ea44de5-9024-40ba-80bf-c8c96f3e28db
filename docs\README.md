# ShipAny 项目文档

欢迎来到 ShipAny 项目文档中心！这里包含了项目开发、测试、部署和维护的完整指南。

## 📚 文档导航

### 🚀 快速开始

- **[快速参考指南](QUICK_REFERENCE.md)** - 常用命令和工作流速查表
- **[开发部署指南](DEVELOPMENT_DEPLOYMENT_GUIDE.md)** - 详细的开发和部署流程

### 🧪 测试指南

- **[测试指南](TESTING_GUIDE.md)** - 完整的测试策略和测试流程

### 📝 内容管理

- **[MDX 内容指南](cms/MDX_CONTENT_GUIDE.md)** - MDX 多语言内容管理系统使用指南
- **[图片管理指南](cms/IMAGE_MANAGEMENT_GUIDE.md)** - 图片资源管理和优化指南
- **[视频嵌入指南](cms/VIDEO_EMBEDDING_GUIDE.md)** - 视频内容嵌入和管理指南

### 💻 开发规范

- **[代码注释指南](CODE_COMMENTS_GUIDE.md)** - 代码注释和文档编写规范

## 🎯 根据角色选择文档

### 👨‍💻 开发者

**新手开发者**：

1. [快速参考指南](QUICK_REFERENCE.md) - 了解基本命令
2. [开发部署指南](DEVELOPMENT_DEPLOYMENT_GUIDE.md) - 学习完整工作流
3. [MDX 内容指南](cms/MDX_CONTENT_GUIDE.md) - 了解内容系统

**经验丰富的开发者**：

1. [快速参考指南](QUICK_REFERENCE.md) - 命令速查
2. [测试指南](TESTING_GUIDE.md) - 测试策略
3. [代码注释指南](CODE_COMMENTS_GUIDE.md) - 代码规范

### ✍️ 内容创作者

1. [MDX 内容指南](cms/MDX_CONTENT_GUIDE.md) - 内容创作流程
2. [图片管理指南](cms/IMAGE_MANAGEMENT_GUIDE.md) - 图片处理
3. [快速参考指南](QUICK_REFERENCE.md) - 基本命令

### 🚀 运维人员

1. [开发部署指南](DEVELOPMENT_DEPLOYMENT_GUIDE.md) - 部署流程
2. [测试指南](TESTING_GUIDE.md) - 测试验证
3. [快速参考指南](QUICK_REFERENCE.md) - 故障排除

## 🔧 核心技术栈

### 前端框架

- **Next.js 15** - React 全栈框架
- **React 19** - 用户界面库
- **TypeScript** - 类型安全的 JavaScript

### 内容管理

- **Contentlayer** - MDX 内容处理
- **MDX** - Markdown + React 组件
- **next-intl** - 国际化支持

### 样式和 UI

- **Tailwind CSS** - 实用优先的 CSS 框架
- **Radix UI** - 无样式的 UI 组件
- **Framer Motion** - 动画库

### 数据库

- **PostgreSQL** - 关系型数据库
- **Drizzle ORM** - TypeScript ORM
- **Drizzle Kit** - 数据库迁移工具

### 开发工具

- **pnpm** - 包管理器
- **ESLint** - 代码检查
- **Turbopack** - 快速构建工具

## 📋 常用命令速查

### 开发环境

```bash
pnpm dev                    # 启动开发服务器
pnpm build                  # 生产构建
pnpm start                  # 启动生产服务器
```

### 内容管理

```bash
contentlayer build         # 处理 MDX 内容
pnpm generate:content      # 生成 SEO 文件
pnpm generate:sitemap      # 生成站点地图
pnpm generate:rss          # 生成 RSS 订阅
```

### 数据库操作

```bash
pnpm db:generate           # 生成迁移文件
pnpm db:migrate            # 执行迁移
pnpm db:studio             # 数据库管理界面
```

### 代码质量

```bash
pnpm lint                  # 代码检查
pnpm test                  # 运行测试
pnpm analyze               # 构建分析
```

## 🏗️ 项目架构概览

### 目录结构

```text
shipany-stwd/
├── src/                   # 源代码
│   ├── app/              # Next.js App Router
│   ├── components/       # React 组件
│   ├── lib/              # 工具函数
│   └── db/               # 数据库配置
├── content/              # MDX 内容文件
│   ├── blogs/           # 博客文章
│   ├── products/        # 产品页面
│   └── case-studies/    # 案例研究
├── public/              # 静态资源
├── docs/                # 项目文档
└── scripts/             # 构建脚本
```

### 内容类型

- **Blogs** - 博客文章和教程
- **Products** - 产品展示和介绍
- **Case Studies** - 客户案例和成功故事
- **Posts** - 数据库驱动的文章（现有功能）

### 多语言支持

- **英文** (en) - 默认语言，无 URL 前缀
- **中文** (zh) - 使用 `/zh/` URL 前缀
- 支持扩展更多语言

## 🔄 工作流程概览

### 开发流程

```
编辑代码/内容 → 自动处理 → 热重载 → 浏览器更新
```

### 内容发布流程

```text
创建 MDX → 内容处理 → SEO 生成 → 构建部署
```

### 部署流程

```text
代码提交 → CI/CD 构建 → 测试验证 → 生产部署
```

## 🆘 获取帮助

### 常见问题

1. **构建失败** - 查看 [故障排除](DEVELOPMENT_DEPLOYMENT_GUIDE.md#故障排除)
2. **内容不显示** - 检查 [内容验证](TESTING_GUIDE.md#内容质量测试)
3. **视频无法播放** - 参考 [视频嵌入指南 - 故障排查](cms/VIDEO_EMBEDDING_GUIDE.md#故障排查)
4. **性能问题** - 参考 [性能优化](DEVELOPMENT_DEPLOYMENT_GUIDE.md#性能优化)

### 调试技巧

1. 查看开发服务器控制台输出
2. 检查 `.contentlayer/generated/` 目录
3. 验证环境变量设置
4. 使用浏览器开发者工具

### 外部资源

- [Next.js 官方文档](https://nextjs.org/docs)
- [Contentlayer 文档](https://contentlayer.dev)
- [Tailwind CSS 文档](https://tailwindcss.com/docs)
- [MDX 文档](https://mdxjs.com)

## 📝 贡献指南

### 文档更新

1. 遵循现有的文档结构和格式
2. 使用清晰的标题和章节划分
3. 提供实际的代码示例
4. 包含必要的截图和图表

### 代码贡献

1. 遵循 [代码注释指南](CODE_COMMENTS_GUIDE.md)
2. 编写相应的测试用例
3. 更新相关文档
4. 确保通过所有检查

## 📊 项目状态

### 功能完成度

- ✅ MDX 多语言内容系统
- ✅ 自动 SEO 优化（sitemap, RSS）
- ✅ 类型安全的路由系统
- ✅ 响应式设计
- ✅ 数据库集成
- ✅ 国际化支持

### 正在开发

- 🔄 自动化测试套件
- 🔄 性能监控
- 🔄 内容管理界面

### 计划功能

- 📋 评论系统
- 📋 搜索功能
- 📋 内容推荐
- 📋 分析统计

---

💡 **提示**: 建议新团队成员先阅读 [快速参考指南](QUICK_REFERENCE.md)，然后根据角色选择相应的详细文档。

🔗 **相关链接**:

- [项目仓库](https://github.com/your-org/shipany-stwd)
- [在线演示](https://shipany.ai)
- [问题反馈](https://github.com/your-org/shipany-stwd/issues)
